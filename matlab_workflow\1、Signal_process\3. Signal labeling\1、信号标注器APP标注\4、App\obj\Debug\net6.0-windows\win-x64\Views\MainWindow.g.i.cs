﻿#pragma checksum "..\..\..\..\..\Views\MainWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "2FB00165B7E7269720D32254AB44F2716B7228DF"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace BowelSoundLabeler.Views {
    
    
    /// <summary>
    /// MainWindow
    /// </summary>
    public partial class MainWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 76 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SetDefaultFolderBtn;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearDefaultFolderBtn;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CurrentFolderLabel;
        
        #line default
        #line hidden
        
        
        #line 112 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectSingleFileBtn;
        
        #line default
        #line hidden
        
        
        #line 120 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SelectMultipleFilesBtn;
        
        #line default
        #line hidden
        
        
        #line 134 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListBox FileListBox;
        
        #line default
        #line hidden
        
        
        #line 152 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button StartLabelingBtn;
        
        #line default
        #line hidden
        
        
        #line 165 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusLabel;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\MainWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar ProgressBar;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/BowelSoundLabeler;V1.0.0.0;component/views/mainwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\MainWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.3.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.SetDefaultFolderBtn = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\..\..\Views\MainWindow.xaml"
            this.SetDefaultFolderBtn.Click += new System.Windows.RoutedEventHandler(this.SetDefaultFolderBtn_Click);
            
            #line default
            #line hidden
            return;
            case 2:
            this.ClearDefaultFolderBtn = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\..\Views\MainWindow.xaml"
            this.ClearDefaultFolderBtn.Click += new System.Windows.RoutedEventHandler(this.ClearDefaultFolderBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.CurrentFolderLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SelectSingleFileBtn = ((System.Windows.Controls.Button)(target));
            
            #line 117 "..\..\..\..\..\Views\MainWindow.xaml"
            this.SelectSingleFileBtn.Click += new System.Windows.RoutedEventHandler(this.SelectSingleFileBtn_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            this.SelectMultipleFilesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 125 "..\..\..\..\..\Views\MainWindow.xaml"
            this.SelectMultipleFilesBtn.Click += new System.Windows.RoutedEventHandler(this.SelectMultipleFilesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.FileListBox = ((System.Windows.Controls.ListBox)(target));
            return;
            case 7:
            this.StartLabelingBtn = ((System.Windows.Controls.Button)(target));
            
            #line 161 "..\..\..\..\..\Views\MainWindow.xaml"
            this.StartLabelingBtn.Click += new System.Windows.RoutedEventHandler(this.StartLabelingBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.StatusLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

