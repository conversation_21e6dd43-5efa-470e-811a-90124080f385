# 肠鸣音标注器 - 快速启动指南

## 🚀 立即开始

### 1. 使用 Visual Studio 2022 打开项目

1. **启动 Visual Studio 2022**
2. **打开项目**：
   - 点击 "打开项目或解决方案"
   - 导航到此文件夹
   - 选择 `BowelSoundLabeler.csproj` 文件
   - 点击 "打开"

### 2. 编译和运行

#### 方法一：使用 Visual Studio（推荐）
1. 按 `F5` 键或点击 "开始调试" 按钮
2. Visual Studio 会自动：
   - 还原 NuGet 包
   - 编译项目
   - 启动应用程序

#### 方法二：使用命令行
```bash
# 在项目文件夹中打开命令提示符或 PowerShell
dotnet restore
dotnet build
dotnet run
```

#### 方法三：使用构建脚本
```bash
# 双击运行 build.bat 文件
build.bat
```

### 3. 发布独立可执行文件

#### 使用 Visual Studio
1. 右键点击项目 → "发布"
2. 选择 "文件夹" 目标
3. 配置发布设置：
   - 目标运行时：`win-x64`
   - 部署模式：`独立`
   - 生成单个文件：`是`
4. 点击 "发布"

#### 使用命令行
```bash
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

发布后的文件位置：
```
bin\Release\net6.0-windows\win-x64\publish\BowelSoundLabeler.exe
```

## 📋 系统要求

### 开发环境
- **Visual Studio 2022** (Community 版本免费)
- **.NET 6.0 SDK** 或更高版本
- **Windows 10** 或更高版本

### 运行环境
- **Windows 10** 或更高版本
- **.NET 6.0 运行时**（自包含部署时不需要）

## 🔧 故障排除

### 常见问题

#### 1. "找不到 .NET SDK"
**解决方案**：
- 下载并安装 [.NET 6.0 SDK](https://dotnet.microsoft.com/download/dotnet/6.0)
- 重启 Visual Studio

#### 2. "NuGet 包还原失败"
**解决方案**：
```bash
# 清理并重新还原
dotnet clean
dotnet restore
```

#### 3. "编译错误"
**解决方案**：
- 检查所有文件是否完整
- 确保项目文件 `.csproj` 没有损坏
- 尝试重新生成解决方案（Ctrl+Shift+B）

#### 4. "应用程序无法启动"
**解决方案**：
- 检查 Windows 版本是否支持
- 确保没有杀毒软件阻止运行
- 查看 Windows 事件日志获取详细错误信息

## 📁 项目结构说明

```
BowelSoundLabeler/
├── 📁 Views/           # 用户界面文件
├── 📁 Models/          # 数据模型
├── 📁 Services/        # 业务逻辑服务
├── 📁 Styles/          # UI 样式资源
├── 📁 Resources/       # 图标等资源文件
├── 📄 App.xaml         # 应用程序入口
└── 📄 *.csproj         # 项目配置文件
```

## 🎯 使用说明

### 基本操作流程
1. **启动应用程序**
2. **设置默认文件夹**（可选）
3. **选择要标注的文件**
4. **点击"开始标注"**
5. **对每个文件进行标注**：
   - 选择是否包含肠鸣音
   - 如果包含，输入数量
6. **查看处理结果**

### 支持的文件格式
- `.mat` - MATLAB 数据文件
- `.wav` - WAV 音频文件
- `.mp3` - MP3 音频文件
- `.m4a` - M4A 音频文件
- `.flac` - FLAC 音频文件

### 输出文件命名规则
- **无肠鸣音**：`原文件名_no.扩展名`
- **有肠鸣音**：`原文件名_yes_N.扩展名`（N为数量）

## 🔍 调试技巧

### 查看日志
应用程序运行时会生成日志文件：
```
%APPDATA%\BowelSoundLabeler\Logs\
```

### 查看配置
应用程序配置文件位置：
```
%APPDATA%\BowelSoundLabeler\appsettings.json
```

### Visual Studio 调试
1. 在代码中设置断点（F9）
2. 按 F5 启动调试
3. 使用调试窗口查看变量值

## 📞 获取帮助

如果遇到问题：

1. **查看日志文件** - 获取详细错误信息
2. **检查系统要求** - 确保环境配置正确
3. **重新编译** - 清理并重新生成项目
4. **联系开发团队** - 提供详细的错误信息和日志

---

**提示**：首次运行建议使用 Visual Studio 的调试模式，这样可以看到详细的错误信息和调试输出。
