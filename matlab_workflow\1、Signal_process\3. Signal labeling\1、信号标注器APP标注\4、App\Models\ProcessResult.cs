namespace BowelSoundLabeler.Models
{
    /// <summary>
    /// 处理状态枚举
    /// </summary>
    public enum ProcessStatus
    {
        /// <summary>
        /// 处理成功
        /// </summary>
        Processed,
        
        /// <summary>
        /// 跳过处理
        /// </summary>
        Skipped,
        
        /// <summary>
        /// 处理出错
        /// </summary>
        Error
    }

    /// <summary>
    /// 文件处理结果
    /// </summary>
    public class ProcessResult
    {
        /// <summary>
        /// 处理状态
        /// </summary>
        public ProcessStatus Status { get; set; }

        /// <summary>
        /// 错误消息（如果有错误）
        /// </summary>
        public string ErrorMessage { get; set; }

        /// <summary>
        /// 原始文件路径
        /// </summary>
        public string OriginalFilePath { get; set; }

        /// <summary>
        /// 新文件路径
        /// </summary>
        public string NewFilePath { get; set; }

        /// <summary>
        /// 是否包含肠鸣音
        /// </summary>
        public bool HasBowelSound { get; set; }

        /// <summary>
        /// 肠鸣音数量
        /// </summary>
        public int BowelSoundCount { get; set; }

        /// <summary>
        /// 处理时间
        /// </summary>
        public System.DateTime ProcessTime { get; set; }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ProcessResult()
        {
            ProcessTime = System.DateTime.Now;
        }
    }
}
