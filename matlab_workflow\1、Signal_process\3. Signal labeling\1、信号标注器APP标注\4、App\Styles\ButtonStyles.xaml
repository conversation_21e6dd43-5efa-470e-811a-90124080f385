<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- WinUI 3 现代化按钮样式 -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="Margin" Value="6"/>
        <Setter Property="CornerRadius" Value="4"/>
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="VerticalAlignment" Value="Center"/>
    </Style>

    <!-- WinUI 3 主要按钮样式 -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="{ThemeResource AccentButtonBackground}"/>
        <Setter Property="Foreground" Value="{ThemeResource AccentButtonForeground}"/>
    </Style>

    <!-- WinUI 3 次要按钮样式 -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="{ThemeResource ButtonBackground}"/>
        <Setter Property="Foreground" Value="{ThemeResource ButtonForeground}"/>
    </Style>

    <!-- WinUI 3 危险按钮样式 -->
    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="#D13438"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- WinUI 3 警告按钮样式 -->
    <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="#FF8C00"/>
        <Setter Property="Foreground" Value="White"/>
    </Style>

    <!-- WinUI 3 中性按钮样式 -->
    <Style x:Key="NeutralButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="{ThemeResource ButtonBackground}"/>
        <Setter Property="Foreground" Value="{ThemeResource ButtonForeground}"/>
    </Style>

    <!-- WinUI 3 轮廓按钮样式 -->
    <Style x:Key="OutlineButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="{ThemeResource AccentButtonBackground}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Foreground" Value="{ThemeResource AccentButtonBackground}"/>
    </Style>

    <!-- 小按钮样式 -->
    <Style x:Key="SmallButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="MinWidth" Value="80"/>
        <Setter Property="Height" Value="28"/>
    </Style>

    <!-- 大按钮样式 -->
    <Style x:Key="LargeButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="20,12"/>
        <Setter Property="MinWidth" Value="140"/>
        <Setter Property="Height" Value="48"/>
    </Style>

</ResourceDictionary>
