<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 现代化按钮样式 -->
    <Style x:Key="ModernButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="White"/>
        <Setter Property="Background" Value="#2E7D32"/>
        <Setter Property="BorderBrush" Value="#2E7D32"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter Name="ContentPresenter"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        RecognizesAccessKey="True"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#388E3C"/>
                            <Setter TargetName="Border" Property="BorderBrush" Value="#388E3C"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#1B5E20"/>
                            <Setter TargetName="Border" Property="BorderBrush" Value="#1B5E20"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="Background" Value="#BDBDBD"/>
                            <Setter TargetName="Border" Property="BorderBrush" Value="#BDBDBD"/>
                            <Setter Property="Foreground" Value="#757575"/>
                            <Setter Property="Cursor" Value="Arrow"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 主要按钮样式（绿色） -->
    <Style x:Key="PrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="#4CAF50"/>
        <Setter Property="BorderBrush" Value="#4CAF50"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#66BB6A"/>
                <Setter Property="BorderBrush" Value="#66BB6A"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#388E3C"/>
                <Setter Property="BorderBrush" Value="#388E3C"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 次要按钮样式（蓝色） -->
    <Style x:Key="SecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="#2196F3"/>
        <Setter Property="BorderBrush" Value="#2196F3"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#42A5F5"/>
                <Setter Property="BorderBrush" Value="#42A5F5"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#1976D2"/>
                <Setter Property="BorderBrush" Value="#1976D2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 危险按钮样式（红色） -->
    <Style x:Key="DangerButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="#F44336"/>
        <Setter Property="BorderBrush" Value="#F44336"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#EF5350"/>
                <Setter Property="BorderBrush" Value="#EF5350"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#D32F2F"/>
                <Setter Property="BorderBrush" Value="#D32F2F"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 警告按钮样式（橙色） -->
    <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="#FF9800"/>
        <Setter Property="BorderBrush" Value="#FF9800"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#FFB74D"/>
                <Setter Property="BorderBrush" Value="#FFB74D"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#F57C00"/>
                <Setter Property="BorderBrush" Value="#F57C00"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 中性按钮样式（灰色） -->
    <Style x:Key="NeutralButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="Background" Value="#9E9E9E"/>
        <Setter Property="BorderBrush" Value="#9E9E9E"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="#BDBDBD"/>
                <Setter Property="BorderBrush" Value="#BDBDBD"/>
            </Trigger>
            <Trigger Property="IsPressed" Value="True">
                <Setter Property="Background" Value="#757575"/>
                <Setter Property="BorderBrush" Value="#757575"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 轮廓按钮样式 -->
    <Style x:Key="OutlineButtonStyle" TargetType="Button">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="#2E7D32"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderBrush" Value="#2E7D32"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="12,6"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Cursor" Value="Hand"/>
        <Setter Property="HorizontalContentAlignment" Value="Center"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter Name="ContentPresenter"
                                        HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                        RecognizesAccessKey="True"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#E8F5E8"/>
                        </Trigger>
                        <Trigger Property="IsPressed" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#C8E6C9"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="BorderBrush" Value="#BDBDBD"/>
                            <Setter Property="Foreground" Value="#BDBDBD"/>
                            <Setter Property="Cursor" Value="Arrow"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 小按钮样式 -->
    <Style x:Key="SmallButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="MinWidth" Value="60"/>
        <Setter Property="Height" Value="24"/>
    </Style>

    <!-- 大按钮样式 -->
    <Style x:Key="LargeButtonStyle" TargetType="Button" BasedOn="{StaticResource ModernButtonStyle}">
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Padding" Value="16,10"/>
        <Setter Property="MinWidth" Value="120"/>
        <Setter Property="Height" Value="44"/>
    </Style>

</ResourceDictionary>
