using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.UI.Xaml;
using Microsoft.UI.Xaml.Controls;
using Windows.Storage.Pickers;
using Windows.Storage;
using BowelSoundLabeler.Services;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Views
{
    /// <summary>
    /// 主窗口交互逻辑
    /// </summary>
    public sealed partial class MainWindow : Window
    {
        private List<string> _selectedFiles;
        private readonly FileService _fileService;
        private readonly ConfigService _configService;
        private bool _isProcessing;

        public MainWindow()
        {
            InitializeComponent();
            
            _selectedFiles = new List<string>();
            _fileService = new FileService();
            _configService = ConfigService.Instance;
            
            InitializeWindow();
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        private void InitializeWindow()
        {
            try
            {
                // 更新默认文件夹显示
                UpdateCurrentFolderDisplay();
                
                // 初始化文件列表
                UpdateFileList();
                
                LogService.Info("主窗口初始化完成");
            }
            catch (Exception ex)
            {
                LogService.Error($"主窗口初始化失败：{ex.Message}");
                ShowErrorMessage("窗口初始化失败", ex.Message);
            }
        }

        /// <summary>
        /// 设置默认文件夹按钮点击事件
        /// </summary>
        private async void SetDefaultFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var folderPicker = new FolderPicker();
                folderPicker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;
                folderPicker.FileTypeFilter.Add("*");

                // 获取窗口句柄用于 WinUI 3
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(this);
                WinRT.Interop.InitializeWithWindow.Initialize(folderPicker, hwnd);

                var folder = await folderPicker.PickSingleFolderAsync();
                if (folder != null)
                {
                    _configService.SetDefaultFolder(folder.Path);
                    UpdateCurrentFolderDisplay();
                    UpdateStatus("默认文件夹已更新");

                    LogService.Info($"默认文件夹已设置为：{folder.Path}");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"设置默认文件夹失败：{ex.Message}");
                await ShowErrorMessageAsync("设置默认文件夹失败", ex.Message);
            }
        }

        /// <summary>
        /// 清除默认文件夹按钮点击事件
        /// </summary>
        private async void ClearDefaultFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = await ShowConfirmDialogAsync(
                    "确认操作",
                    "确定要清除默认文件夹设置吗？");

                if (result)
                {
                    _configService.ClearDefaultFolder();
                    UpdateCurrentFolderDisplay();
                    UpdateStatus("默认文件夹设置已清除");

                    LogService.Info("默认文件夹设置已清除");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"清除默认文件夹失败：{ex.Message}");
                await ShowErrorMessageAsync("清除默认文件夹失败", ex.Message);
            }
        }

        /// <summary>
        /// 选择单个文件按钮点击事件
        /// </summary>
        private async void SelectSingleFileBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePicker = new FileOpenPicker();
                filePicker.ViewMode = PickerViewMode.List;
                filePicker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;

                // 添加支持的文件类型
                filePicker.FileTypeFilter.Add(".mat");
                filePicker.FileTypeFilter.Add(".wav");
                filePicker.FileTypeFilter.Add(".mp3");
                filePicker.FileTypeFilter.Add(".m4a");
                filePicker.FileTypeFilter.Add(".flac");

                // 获取窗口句柄用于 WinUI 3
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(this);
                WinRT.Interop.InitializeWithWindow.Initialize(filePicker, hwnd);

                var file = await filePicker.PickSingleFileAsync();
                if (file != null)
                {
                    _selectedFiles.Clear();
                    _selectedFiles.Add(file.Path);

                    // 保存用户选择的文件夹作为新的默认路径
                    var selectedFolder = Path.GetDirectoryName(file.Path);
                    _configService.SetDefaultFolder(selectedFolder);

                    UpdateCurrentFolderDisplay();
                    UpdateFileList();
                    StartLabelingBtn.IsEnabled = true;
                    UpdateStatus("已选择1个文件");

                    LogService.Info($"已选择单个文件：{file.Path}");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"选择单个文件失败：{ex.Message}");
                await ShowErrorMessageAsync("选择文件失败", ex.Message);
            }
        }

        /// <summary>
        /// 选择多个文件按钮点击事件
        /// </summary>
        private async void SelectMultipleFilesBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var filePicker = new FileOpenPicker();
                filePicker.ViewMode = PickerViewMode.List;
                filePicker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;

                // 添加支持的文件类型
                filePicker.FileTypeFilter.Add(".mat");
                filePicker.FileTypeFilter.Add(".wav");
                filePicker.FileTypeFilter.Add(".mp3");
                filePicker.FileTypeFilter.Add(".m4a");
                filePicker.FileTypeFilter.Add(".flac");

                // 获取窗口句柄用于 WinUI 3
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(this);
                WinRT.Interop.InitializeWithWindow.Initialize(filePicker, hwnd);

                var files = await filePicker.PickMultipleFilesAsync();
                if (files != null && files.Count > 0)
                {
                    _selectedFiles.Clear();
                    foreach (var file in files)
                    {
                        _selectedFiles.Add(file.Path);
                    }

                    // 保存用户选择的文件夹作为新的默认路径
                    if (files.Count > 0)
                    {
                        var selectedFolder = Path.GetDirectoryName(files[0].Path);
                        _configService.SetDefaultFolder(selectedFolder);
                    }

                    UpdateCurrentFolderDisplay();
                    UpdateFileList();
                    StartLabelingBtn.IsEnabled = true;
                    UpdateStatus($"已选择{_selectedFiles.Count}个文件");

                    LogService.Info($"已选择{_selectedFiles.Count}个文件");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"选择多个文件失败：{ex.Message}");
                await ShowErrorMessageAsync("选择文件失败", ex.Message);
            }
        }

        /// <summary>
        /// 开始标注按钮点击事件
        /// </summary>
        private async void StartLabelingBtn_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedFiles == null || _selectedFiles.Count == 0)
            {
                ShowWarningMessage("请先选择文件");
                return;
            }

            if (_isProcessing)
            {
                ShowWarningMessage("正在处理中，请稍候...");
                return;
            }

            try
            {
                await StartLabelingProcess();
            }
            catch (Exception ex)
            {
                LogService.Error($"标注过程失败：{ex.Message}");
                ShowErrorMessage("标注过程失败", ex.Message);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        /// <summary>
        /// 开始标注处理过程
        /// </summary>
        private async Task StartLabelingProcess()
        {
            SetProcessingState(true);

            var processedCount = 0;
            var skippedCount = 0;
            var errorCount = 0;

            try
            {
                ProgressBar.Maximum = _selectedFiles.Count;
                ProgressBar.Value = 0;
                ProgressBar.Visibility = Visibility.Visible;

                for (int i = 0; i < _selectedFiles.Count; i++)
                {
                    var currentFile = _selectedFiles[i];
                    var fileName = Path.GetFileNameWithoutExtension(currentFile);
                    var extension = Path.GetExtension(currentFile);

                    UpdateStatus($"正在处理文件 {i + 1}/{_selectedFiles.Count}: {fileName}{extension}");

                    try
                    {
                        var result = await ProcessSingleFile(currentFile);

                        switch (result.Status)
                        {
                            case ProcessStatus.Processed:
                                processedCount++;
                                break;
                            case ProcessStatus.Skipped:
                                skippedCount++;
                                break;
                            case ProcessStatus.Error:
                                errorCount++;
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogService.Error($"处理文件 {currentFile} 时发生错误：{ex.Message}");
                        errorCount++;
                    }

                    ProgressBar.Value = i + 1;

                    // 允许UI更新
                    await Task.Delay(10);
                }

                // 显示完成信息
                var resultMessage = $"标注完成！\n\n" +
                                  $"处理成功: {processedCount} 个文件\n" +
                                  $"跳过: {skippedCount} 个文件\n" +
                                  $"错误: {errorCount} 个文件";

                MessageBox.Show(resultMessage, "完成", MessageBoxButton.OK, MessageBoxImage.Information);

                LogService.Info($"标注完成 - 成功:{processedCount}, 跳过:{skippedCount}, 错误:{errorCount}");
            }
            finally
            {
                ProgressBar.Visibility = Visibility.Collapsed;

                // 清空文件列表
                _selectedFiles.Clear();
                UpdateFileList();
                StartLabelingBtn.IsEnabled = false;
                UpdateStatus("标注完成，就绪");
            }
        }

        /// <summary>
        /// 处理单个文件
        /// </summary>
        private async Task<ProcessResult> ProcessSingleFile(string filePath)
        {
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            var extension = Path.GetExtension(filePath);
            var directory = Path.GetDirectoryName(filePath);

            // 询问是否包含肠鸣音
            var labelDialog = new LabelingDialog(fileName + extension);
            var dialogResult = labelDialog.ShowDialog();

            if (dialogResult != true)
            {
                return new ProcessResult { Status = ProcessStatus.Skipped };
            }

            try
            {
                string newFileName;

                if (labelDialog.HasBowelSound)
                {
                    // 有肠鸣音，添加_yes_N后缀
                    newFileName = $"{fileName}_yes_{labelDialog.BowelSoundCount}{extension}";
                }
                else
                {
                    // 无肠鸣音，添加_no后缀
                    newFileName = $"{fileName}_no{extension}";
                }

                var newFilePath = Path.Combine(directory, newFileName);

                // 检查文件是否已存在
                if (File.Exists(newFilePath))
                {
                    var overwriteResult = MessageBox.Show(
                        $"文件 {newFileName} 已存在，是否覆盖？",
                        "文件已存在",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (overwriteResult != MessageBoxResult.Yes)
                    {
                        return new ProcessResult { Status = ProcessStatus.Skipped };
                    }

                    // 删除已存在的文件
                    File.Delete(newFilePath);
                }

                // 重命名文件
                File.Move(filePath, newFilePath);

                LogService.Info($"文件重命名成功：{filePath} -> {newFilePath}");

                return new ProcessResult { Status = ProcessStatus.Processed };
            }
            catch (Exception ex)
            {
                LogService.Error($"处理文件 {filePath} 时发生错误：{ex.Message}");
                ShowErrorMessage($"处理文件 {fileName}{extension} 时出错", ex.Message);

                return new ProcessResult { Status = ProcessStatus.Error, ErrorMessage = ex.Message };
            }
        }

        /// <summary>
        /// 更新当前文件夹显示
        /// </summary>
        private void UpdateCurrentFolderDisplay()
        {
            try
            {
                var defaultPath = _configService.GetDefaultFolder();

                if (!string.IsNullOrEmpty(defaultPath))
                {
                    // 显示路径，如果太长则截断
                    var displayPath = defaultPath.Length > 60
                        ? "..." + defaultPath.Substring(defaultPath.Length - 57)
                        : defaultPath;

                    CurrentFolderLabel.Text = $"当前默认文件夹: {displayPath}";
                    CurrentFolderLabel.Foreground = (System.Windows.Media.Brush)FindResource("PrimaryBrush");
                }
                else
                {
                    CurrentFolderLabel.Text = "当前默认文件夹: 未设置";
                    CurrentFolderLabel.Foreground = (System.Windows.Media.Brush)FindResource("DarkGrayBrush");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"更新文件夹显示失败：{ex.Message}");
                CurrentFolderLabel.Text = "当前默认文件夹: 获取失败";
                CurrentFolderLabel.Foreground = (System.Windows.Media.Brush)FindResource("DangerBrush");
            }
        }

        /// <summary>
        /// 更新文件列表显示
        /// </summary>
        private void UpdateFileList()
        {
            try
            {
                FileListBox.Items.Clear();

                if (_selectedFiles == null || _selectedFiles.Count == 0)
                {
                    FileListBox.Items.Add("选择文件后将在此显示文件列表...");
                }
                else
                {
                    for (int i = 0; i < _selectedFiles.Count; i++)
                    {
                        var fileName = Path.GetFileName(_selectedFiles[i]);
                        FileListBox.Items.Add($"{i + 1}. {fileName}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"更新文件列表失败：{ex.Message}");
                FileListBox.Items.Clear();
                FileListBox.Items.Add("文件列表更新失败");
            }
        }

        /// <summary>
        /// 设置处理状态
        /// </summary>
        private void SetProcessingState(bool isProcessing)
        {
            _isProcessing = isProcessing;

            StartLabelingBtn.IsEnabled = !isProcessing && _selectedFiles.Count > 0;
            SelectSingleFileBtn.IsEnabled = !isProcessing;
            SelectMultipleFilesBtn.IsEnabled = !isProcessing;
            SetDefaultFolderBtn.IsEnabled = !isProcessing;
            ClearDefaultFolderBtn.IsEnabled = !isProcessing;
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus(string message)
        {
            StatusLabel.Text = message;
            LogService.Info($"状态更新：{message}");
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        private async Task ShowErrorMessageAsync(string title, string message)
        {
            var dialog = new ContentDialog()
            {
                Title = title,
                Content = message,
                CloseButtonText = "确定",
                XamlRoot = this.Content.XamlRoot
            };

            await dialog.ShowAsync();
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        private async Task ShowWarningMessageAsync(string message)
        {
            var dialog = new ContentDialog()
            {
                Title = "提示",
                Content = message,
                CloseButtonText = "确定",
                XamlRoot = this.Content.XamlRoot
            };

            await dialog.ShowAsync();
        }

        /// <summary>
        /// 显示确认对话框
        /// </summary>
        private async Task<bool> ShowConfirmDialogAsync(string title, string message)
        {
            var dialog = new ContentDialog()
            {
                Title = title,
                Content = message,
                PrimaryButtonText = "是",
                CloseButtonText = "否",
                XamlRoot = this.Content.XamlRoot
            };

            var result = await dialog.ShowAsync();
            return result == ContentDialogResult.Primary;
        }
    }
}
