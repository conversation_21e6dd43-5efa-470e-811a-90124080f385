using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using BowelSoundLabeler.Services;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Views
{
    /// <summary>
    /// 主窗口交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private List<string> _selectedFiles;
        private readonly FileService _fileService;
        private readonly ConfigService _configService;
        private bool _isProcessing;

        public MainWindow()
        {
            InitializeComponent();
            
            _selectedFiles = new List<string>();
            _fileService = new FileService();
            _configService = ConfigService.Instance;
            
            InitializeWindow();
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        private void InitializeWindow()
        {
            try
            {
                // 更新默认文件夹显示
                UpdateCurrentFolderDisplay();
                
                // 初始化文件列表
                UpdateFileList();
                
                LogService.Info("主窗口初始化完成");
            }
            catch (Exception ex)
            {
                LogService.Error($"主窗口初始化失败：{ex.Message}");
                ShowErrorMessage("窗口初始化失败", ex.Message);
            }
        }

        /// <summary>
        /// 设置默认文件夹按钮点击事件
        /// </summary>
        private void SetDefaultFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new System.Windows.Forms.FolderBrowserDialog
                {
                    Description = "选择默认工作文件夹",
                    ShowNewFolderButton = true
                };

                // 设置初始路径
                var currentDefault = _configService.GetDefaultFolder();
                if (!string.IsNullOrEmpty(currentDefault) && Directory.Exists(currentDefault))
                {
                    dialog.SelectedPath = currentDefault;
                }

                if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
                {
                    _configService.SetDefaultFolder(dialog.SelectedPath);
                    UpdateCurrentFolderDisplay();
                    UpdateStatus("默认文件夹已更新");
                    
                    LogService.Info($"默认文件夹已设置为：{dialog.SelectedPath}");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"设置默认文件夹失败：{ex.Message}");
                ShowErrorMessage("设置默认文件夹失败", ex.Message);
            }
        }

        /// <summary>
        /// 清除默认文件夹按钮点击事件
        /// </summary>
        private void ClearDefaultFolderBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "确定要清除默认文件夹设置吗？",
                    "确认操作",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    _configService.ClearDefaultFolder();
                    UpdateCurrentFolderDisplay();
                    UpdateStatus("默认文件夹设置已清除");
                    
                    LogService.Info("默认文件夹设置已清除");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"清除默认文件夹失败：{ex.Message}");
                ShowErrorMessage("清除默认文件夹失败", ex.Message);
            }
        }

        /// <summary>
        /// 选择单个文件按钮点击事件
        /// </summary>
        private void SelectSingleFileBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new OpenFileDialog
                {
                    Title = "选择音频/信号文件",
                    Filter = "音频/信号文件|*.mat;*.wav;*.mp3;*.m4a;*.flac|" +
                            "MATLAB文件|*.mat|" +
                            "WAV文件|*.wav|" +
                            "MP3文件|*.mp3|" +
                            "M4A文件|*.m4a|" +
                            "FLAC文件|*.flac|" +
                            "所有文件|*.*",
                    Multiselect = false
                };

                // 设置初始目录
                var defaultFolder = _configService.GetDefaultFolder();
                if (!string.IsNullOrEmpty(defaultFolder) && Directory.Exists(defaultFolder))
                {
                    dialog.InitialDirectory = defaultFolder;
                }

                if (dialog.ShowDialog() == true)
                {
                    _selectedFiles.Clear();
                    _selectedFiles.Add(dialog.FileName);
                    
                    // 保存用户选择的文件夹作为新的默认路径
                    var selectedFolder = Path.GetDirectoryName(dialog.FileName);
                    _configService.SetDefaultFolder(selectedFolder);
                    
                    UpdateCurrentFolderDisplay();
                    UpdateFileList();
                    StartLabelingBtn.IsEnabled = true;
                    UpdateStatus("已选择1个文件");
                    
                    LogService.Info($"已选择单个文件：{dialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"选择单个文件失败：{ex.Message}");
                ShowErrorMessage("选择文件失败", ex.Message);
            }
        }

        /// <summary>
        /// 选择多个文件按钮点击事件
        /// </summary>
        private void SelectMultipleFilesBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var dialog = new OpenFileDialog
                {
                    Title = "选择音频/信号文件",
                    Filter = "音频/信号文件|*.mat;*.wav;*.mp3;*.m4a;*.flac|" +
                            "MATLAB文件|*.mat|" +
                            "WAV文件|*.wav|" +
                            "MP3文件|*.mp3|" +
                            "M4A文件|*.m4a|" +
                            "FLAC文件|*.flac|" +
                            "所有文件|*.*",
                    Multiselect = true
                };

                // 设置初始目录
                var defaultFolder = _configService.GetDefaultFolder();
                if (!string.IsNullOrEmpty(defaultFolder) && Directory.Exists(defaultFolder))
                {
                    dialog.InitialDirectory = defaultFolder;
                }

                if (dialog.ShowDialog() == true)
                {
                    _selectedFiles.Clear();
                    _selectedFiles.AddRange(dialog.FileNames);
                    
                    // 保存用户选择的文件夹作为新的默认路径
                    if (dialog.FileNames.Length > 0)
                    {
                        var selectedFolder = Path.GetDirectoryName(dialog.FileNames[0]);
                        _configService.SetDefaultFolder(selectedFolder);
                    }
                    
                    UpdateCurrentFolderDisplay();
                    UpdateFileList();
                    StartLabelingBtn.IsEnabled = true;
                    UpdateStatus($"已选择{_selectedFiles.Count}个文件");
                    
                    LogService.Info($"已选择{_selectedFiles.Count}个文件");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"选择多个文件失败：{ex.Message}");
                ShowErrorMessage("选择文件失败", ex.Message);
            }
        }

        /// <summary>
        /// 开始标注按钮点击事件
        /// </summary>
        private async void StartLabelingBtn_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedFiles == null || _selectedFiles.Count == 0)
            {
                ShowWarningMessage("请先选择文件");
                return;
            }

            if (_isProcessing)
            {
                ShowWarningMessage("正在处理中，请稍候...");
                return;
            }

            try
            {
                await StartLabelingProcess();
            }
            catch (Exception ex)
            {
                LogService.Error($"标注过程失败：{ex.Message}");
                ShowErrorMessage("标注过程失败", ex.Message);
            }
            finally
            {
                SetProcessingState(false);
            }
        }

        /// <summary>
        /// 开始标注处理过程
        /// </summary>
        private async Task StartLabelingProcess()
        {
            SetProcessingState(true);

            var processedCount = 0;
            var skippedCount = 0;
            var errorCount = 0;

            try
            {
                ProgressBar.Maximum = _selectedFiles.Count;
                ProgressBar.Value = 0;
                ProgressBar.Visibility = Visibility.Visible;

                for (int i = 0; i < _selectedFiles.Count; i++)
                {
                    var currentFile = _selectedFiles[i];
                    var fileName = Path.GetFileNameWithoutExtension(currentFile);
                    var extension = Path.GetExtension(currentFile);

                    UpdateStatus($"正在处理文件 {i + 1}/{_selectedFiles.Count}: {fileName}{extension}");

                    try
                    {
                        var result = await ProcessSingleFile(currentFile);

                        switch (result.Status)
                        {
                            case ProcessStatus.Processed:
                                processedCount++;
                                break;
                            case ProcessStatus.Skipped:
                                skippedCount++;
                                break;
                            case ProcessStatus.Error:
                                errorCount++;
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        LogService.Error($"处理文件 {currentFile} 时发生错误：{ex.Message}");
                        errorCount++;
                    }

                    ProgressBar.Value = i + 1;

                    // 允许UI更新
                    await Task.Delay(10);
                }

                // 显示完成信息
                var resultMessage = $"标注完成！\n\n" +
                                  $"处理成功: {processedCount} 个文件\n" +
                                  $"跳过: {skippedCount} 个文件\n" +
                                  $"错误: {errorCount} 个文件";

                MessageBox.Show(resultMessage, "完成", MessageBoxButton.OK, MessageBoxImage.Information);

                LogService.Info($"标注完成 - 成功:{processedCount}, 跳过:{skippedCount}, 错误:{errorCount}");
            }
            finally
            {
                ProgressBar.Visibility = Visibility.Collapsed;

                // 清空文件列表
                _selectedFiles.Clear();
                UpdateFileList();
                StartLabelingBtn.IsEnabled = false;
                UpdateStatus("标注完成，就绪");
            }
        }

        /// <summary>
        /// 处理单个文件
        /// </summary>
        private async Task<ProcessResult> ProcessSingleFile(string filePath)
        {
            var fileName = Path.GetFileNameWithoutExtension(filePath);
            var extension = Path.GetExtension(filePath);
            var directory = Path.GetDirectoryName(filePath);

            // 询问是否包含肠鸣音
            var labelDialog = new LabelingDialog(fileName + extension);
            var dialogResult = labelDialog.ShowDialog();

            if (dialogResult != true)
            {
                return new ProcessResult { Status = ProcessStatus.Skipped };
            }

            try
            {
                string newFileName;

                if (labelDialog.HasBowelSound)
                {
                    // 有肠鸣音，添加_yes_N后缀
                    newFileName = $"{fileName}_yes_{labelDialog.BowelSoundCount}{extension}";
                }
                else
                {
                    // 无肠鸣音，添加_no后缀
                    newFileName = $"{fileName}_no{extension}";
                }

                var newFilePath = Path.Combine(directory, newFileName);

                // 检查文件是否已存在
                if (File.Exists(newFilePath))
                {
                    var overwriteResult = MessageBox.Show(
                        $"文件 {newFileName} 已存在，是否覆盖？",
                        "文件已存在",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (overwriteResult != MessageBoxResult.Yes)
                    {
                        return new ProcessResult { Status = ProcessStatus.Skipped };
                    }

                    // 删除已存在的文件
                    File.Delete(newFilePath);
                }

                // 重命名文件
                File.Move(filePath, newFilePath);

                LogService.Info($"文件重命名成功：{filePath} -> {newFilePath}");

                return new ProcessResult { Status = ProcessStatus.Processed };
            }
            catch (Exception ex)
            {
                LogService.Error($"处理文件 {filePath} 时发生错误：{ex.Message}");
                ShowErrorMessage($"处理文件 {fileName}{extension} 时出错", ex.Message);

                return new ProcessResult { Status = ProcessStatus.Error, ErrorMessage = ex.Message };
            }
        }

        /// <summary>
        /// 更新当前文件夹显示
        /// </summary>
        private void UpdateCurrentFolderDisplay()
        {
            try
            {
                var defaultPath = _configService.GetDefaultFolder();

                if (!string.IsNullOrEmpty(defaultPath))
                {
                    // 显示路径，如果太长则截断
                    var displayPath = defaultPath.Length > 60
                        ? "..." + defaultPath.Substring(defaultPath.Length - 57)
                        : defaultPath;

                    CurrentFolderLabel.Text = $"当前默认文件夹: {displayPath}";
                    CurrentFolderLabel.Foreground = (System.Windows.Media.Brush)FindResource("PrimaryBrush");
                }
                else
                {
                    CurrentFolderLabel.Text = "当前默认文件夹: 未设置";
                    CurrentFolderLabel.Foreground = (System.Windows.Media.Brush)FindResource("DarkGrayBrush");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"更新文件夹显示失败：{ex.Message}");
                CurrentFolderLabel.Text = "当前默认文件夹: 获取失败";
                CurrentFolderLabel.Foreground = (System.Windows.Media.Brush)FindResource("DangerBrush");
            }
        }

        /// <summary>
        /// 更新文件列表显示
        /// </summary>
        private void UpdateFileList()
        {
            try
            {
                FileListBox.Items.Clear();

                if (_selectedFiles == null || _selectedFiles.Count == 0)
                {
                    FileListBox.Items.Add("选择文件后将在此显示文件列表...");
                }
                else
                {
                    for (int i = 0; i < _selectedFiles.Count; i++)
                    {
                        var fileName = Path.GetFileName(_selectedFiles[i]);
                        FileListBox.Items.Add($"{i + 1}. {fileName}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"更新文件列表失败：{ex.Message}");
                FileListBox.Items.Clear();
                FileListBox.Items.Add("文件列表更新失败");
            }
        }

        /// <summary>
        /// 设置处理状态
        /// </summary>
        private void SetProcessingState(bool isProcessing)
        {
            _isProcessing = isProcessing;

            StartLabelingBtn.IsEnabled = !isProcessing && _selectedFiles.Count > 0;
            SelectSingleFileBtn.IsEnabled = !isProcessing;
            SelectMultipleFilesBtn.IsEnabled = !isProcessing;
            SetDefaultFolderBtn.IsEnabled = !isProcessing;
            ClearDefaultFolderBtn.IsEnabled = !isProcessing;
        }

        /// <summary>
        /// 更新状态显示
        /// </summary>
        private void UpdateStatus(string message)
        {
            StatusLabel.Text = message;
            LogService.Info($"状态更新：{message}");
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        private void ShowErrorMessage(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// 显示警告消息
        /// </summary>
        private void ShowWarningMessage(string message)
        {
            MessageBox.Show(message, "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }
}
