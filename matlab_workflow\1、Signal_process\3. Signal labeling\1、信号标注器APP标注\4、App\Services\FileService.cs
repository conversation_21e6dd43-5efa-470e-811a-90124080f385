using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 文件服务
    /// </summary>
    public class FileService
    {
        /// <summary>
        /// 支持的文件扩展名
        /// </summary>
        private readonly HashSet<string> _supportedExtensions = new HashSet<string>(StringComparer.OrdinalIgnoreCase)
        {
            ".mat", ".wav", ".mp3", ".m4a", ".flac"
        };

        /// <summary>
        /// 验证文件是否为支持的格式
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>是否支持</returns>
        public bool IsSupportedFile(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return false;

                var extension = Path.GetExtension(filePath);
                return _supportedExtensions.Contains(extension);
            }
            catch (Exception ex)
            {
                LogService.Error($"验证文件格式时发生错误：{ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 验证文件是否存在且可访问
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>验证结果</returns>
        public bool ValidateFile(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    return false;

                if (!File.Exists(filePath))
                    return false;

                // 尝试获取文件信息以验证访问权限
                var fileInfo = new FileInfo(filePath);
                return fileInfo.Length >= 0;
            }
            catch (Exception ex)
            {
                LogService.Error($"验证文件时发生错误：{filePath} - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 批量验证文件
        /// </summary>
        /// <param name="filePaths">文件路径列表</param>
        /// <returns>验证结果</returns>
        public Dictionary<string, bool> ValidateFiles(IEnumerable<string> filePaths)
        {
            var results = new Dictionary<string, bool>();

            try
            {
                foreach (var filePath in filePaths ?? Enumerable.Empty<string>())
                {
                    results[filePath] = ValidateFile(filePath) && IsSupportedFile(filePath);
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"批量验证文件时发生错误：{ex.Message}");
            }

            return results;
        }

        /// <summary>
        /// 重命名文件
        /// </summary>
        /// <param name="originalPath">原始文件路径</param>
        /// <param name="newFileName">新文件名（不包含路径）</param>
        /// <returns>新文件路径</returns>
        public string RenameFile(string originalPath, string newFileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(originalPath))
                    throw new ArgumentException("原始文件路径不能为空");

                if (string.IsNullOrWhiteSpace(newFileName))
                    throw new ArgumentException("新文件名不能为空");

                if (!File.Exists(originalPath))
                    throw new FileNotFoundException($"原始文件不存在：{originalPath}");

                var directory = Path.GetDirectoryName(originalPath);
                var newPath = Path.Combine(directory, newFileName);

                // 检查新文件是否已存在
                if (File.Exists(newPath) && !string.Equals(originalPath, newPath, StringComparison.OrdinalIgnoreCase))
                {
                    throw new InvalidOperationException($"目标文件已存在：{newPath}");
                }

                // 如果是相同文件（仅大小写不同），需要特殊处理
                if (string.Equals(originalPath, newPath, StringComparison.OrdinalIgnoreCase) && 
                    originalPath != newPath)
                {
                    var tempPath = newPath + ".tmp";
                    File.Move(originalPath, tempPath);
                    File.Move(tempPath, newPath);
                }
                else if (originalPath != newPath)
                {
                    File.Move(originalPath, newPath);
                }

                LogService.Info($"文件重命名成功：{originalPath} -> {newPath}");
                return newPath;
            }
            catch (Exception ex)
            {
                LogService.Error($"重命名文件失败：{originalPath} -> {newFileName} - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 生成标注后的文件名
        /// </summary>
        /// <param name="originalFileName">原始文件名</param>
        /// <param name="hasBowelSound">是否包含肠鸣音</param>
        /// <param name="bowelSoundCount">肠鸣音数量</param>
        /// <returns>新文件名</returns>
        public string GenerateLabeledFileName(string originalFileName, bool hasBowelSound, int bowelSoundCount = 0)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(originalFileName))
                    throw new ArgumentException("原始文件名不能为空");

                var nameWithoutExtension = Path.GetFileNameWithoutExtension(originalFileName);
                var extension = Path.GetExtension(originalFileName);

                string suffix;
                if (hasBowelSound)
                {
                    if (bowelSoundCount <= 0)
                        throw new ArgumentException("肠鸣音数量必须大于0");
                    
                    suffix = $"_yes_{bowelSoundCount}";
                }
                else
                {
                    suffix = "_no";
                }

                return $"{nameWithoutExtension}{suffix}{extension}";
            }
            catch (Exception ex)
            {
                LogService.Error($"生成标注文件名失败：{originalFileName} - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 检查文件是否已被标注
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>是否已标注</returns>
        public bool IsFileLabeled(string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return false;

                var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);
                
                return nameWithoutExtension.EndsWith("_no", StringComparison.OrdinalIgnoreCase) ||
                       nameWithoutExtension.Contains("_yes_");
            }
            catch (Exception ex)
            {
                LogService.Error($"检查文件标注状态时发生错误：{fileName} - {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 解析已标注文件的信息
        /// </summary>
        /// <param name="fileName">文件名</param>
        /// <returns>标注信息</returns>
        public (bool HasBowelSound, int Count) ParseLabeledFileName(string fileName)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(fileName))
                    return (false, 0);

                var nameWithoutExtension = Path.GetFileNameWithoutExtension(fileName);

                if (nameWithoutExtension.EndsWith("_no", StringComparison.OrdinalIgnoreCase))
                {
                    return (false, 0);
                }

                var yesIndex = nameWithoutExtension.LastIndexOf("_yes_", StringComparison.OrdinalIgnoreCase);
                if (yesIndex >= 0)
                {
                    var countString = nameWithoutExtension.Substring(yesIndex + 5);
                    if (int.TryParse(countString, out int count) && count > 0)
                    {
                        return (true, count);
                    }
                }

                return (false, 0);
            }
            catch (Exception ex)
            {
                LogService.Error($"解析标注文件名时发生错误：{fileName} - {ex.Message}");
                return (false, 0);
            }
        }

        /// <summary>
        /// 获取文件信息
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>文件信息</returns>
        public FileInfo GetFileInfo(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空");

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在：{filePath}");

                return new FileInfo(filePath);
            }
            catch (Exception ex)
            {
                LogService.Error($"获取文件信息失败：{filePath} - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取目录中的支持文件
        /// </summary>
        /// <param name="directoryPath">目录路径</param>
        /// <param name="includeSubdirectories">是否包含子目录</param>
        /// <returns>支持的文件列表</returns>
        public List<string> GetSupportedFilesInDirectory(string directoryPath, bool includeSubdirectories = false)
        {
            var supportedFiles = new List<string>();

            try
            {
                if (string.IsNullOrWhiteSpace(directoryPath))
                    throw new ArgumentException("目录路径不能为空");

                if (!Directory.Exists(directoryPath))
                    throw new DirectoryNotFoundException($"目录不存在：{directoryPath}");

                var searchOption = includeSubdirectories ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var allFiles = Directory.GetFiles(directoryPath, "*.*", searchOption);

                foreach (var file in allFiles)
                {
                    if (IsSupportedFile(file))
                    {
                        supportedFiles.Add(file);
                    }
                }

                LogService.Info($"在目录 {directoryPath} 中找到 {supportedFiles.Count} 个支持的文件");
            }
            catch (Exception ex)
            {
                LogService.Error($"获取目录中的支持文件失败：{directoryPath} - {ex.Message}");
                throw;
            }

            return supportedFiles;
        }

        /// <summary>
        /// 创建文件备份
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <returns>备份文件路径</returns>
        public string CreateBackup(string filePath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(filePath))
                    throw new ArgumentException("文件路径不能为空");

                if (!File.Exists(filePath))
                    throw new FileNotFoundException($"文件不存在：{filePath}");

                var directory = Path.GetDirectoryName(filePath);
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var extension = Path.GetExtension(filePath);
                var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");

                var backupFileName = $"{fileName}.backup.{timestamp}{extension}";
                var backupPath = Path.Combine(directory, backupFileName);

                File.Copy(filePath, backupPath);

                LogService.Info($"文件备份创建成功：{filePath} -> {backupPath}");
                return backupPath;
            }
            catch (Exception ex)
            {
                LogService.Error($"创建文件备份失败：{filePath} - {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取支持的文件扩展名列表
        /// </summary>
        /// <returns>支持的扩展名列表</returns>
        public IReadOnlyCollection<string> GetSupportedExtensions()
        {
            return _supportedExtensions.ToList().AsReadOnly();
        }
    }
}
