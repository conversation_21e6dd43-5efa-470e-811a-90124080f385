# 肠鸣音标注器 (BowelSoundLabeler) - WinUI 3 版本

一个专业的肠鸣音信号标注工具，使用 C# WinUI 3 开发，提供现代化的 Fluent Design 界面和高效的文件处理功能。

## 功能特性

### 核心功能
- **文件选择**：支持单个或批量选择音频/信号文件
- **智能标注**：通过对话框询问用户文件是否包含肠鸣音及数量
- **自动重命名**：根据标注结果自动重命名文件
  - 无肠鸣音：`原文件名_no.扩展名`
  - 有肠鸣音：`原文件名_yes_N.扩展名`（N为数量）
- **文件夹记忆**：自动记住用户常用的工作文件夹

### 支持格式
- MATLAB 文件：`.mat`
- 音频文件：`.wav`, `.mp3`, `.m4a`, `.flac`

### 用户体验
- **Fluent Design** 现代化界面设计
- **Mica 材质背景** 提供沉浸式体验
- **自适应主题** 支持浅色/深色模式
- 实时进度显示
- 详细的状态反馈
- 键盘快捷键支持
- 错误处理和恢复机制

## 系统要求

- **操作系统**：Windows 10 版本 1809 (Build 17763) 或更高版本
- **推荐系统**：Windows 11（获得最佳 Fluent Design 体验）
- **.NET 运行时**：.NET 6.0 或更高版本（自包含部署时不需要）
- **Windows App SDK**：1.4 或更高版本（自包含部署时不需要）
- **内存**：至少 1GB 可用内存
- **磁盘空间**：至少 200MB 可用空间

## 安装和使用

### 开发环境搭建

1. **安装 Visual Studio 2022**
   - 下载并安装 Visual Studio 2022 Community（免费）
   - 确保安装了 ".NET 桌面开发" 工作负载

2. **打开项目**
   ```bash
   # 在 Visual Studio 中打开项目文件
   BowelSoundLabeler.csproj
   ```

3. **还原 NuGet 包**
   ```bash
   # 在包管理器控制台中运行
   dotnet restore
   ```

### 编译和运行

#### 调试模式
```bash
# 在 Visual Studio 中按 F5 或点击"开始调试"
# 或在命令行中运行：
dotnet run
```

#### 发布模式
```bash
# 发布为自包含的可执行文件
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

# 输出文件位置：
# bin\Release\net6.0-windows\win-x64\publish\BowelSoundLabeler.exe
```

### 使用说明

1. **启动应用程序**
   - 双击 `BowelSoundLabeler.exe` 启动程序

2. **设置工作文件夹**（可选）
   - 点击"设置默认文件夹"按钮选择常用的工作目录
   - 程序会记住这个设置，下次启动时自动使用

3. **选择文件**
   - 点击"选择单个文件"或"选择多个文件"
   - 在文件对话框中选择要标注的音频/信号文件

4. **开始标注**
   - 点击"开始标注"按钮
   - 对每个文件，程序会弹出对话框询问：
     - 是否包含肠鸣音？
     - 如果包含，请输入数量
   - 可以选择"跳过"某个文件或"取消"整个过程

5. **查看结果**
   - 程序会自动重命名文件并显示处理统计
   - 处理完成后文件列表会自动清空

## 项目结构

```
BowelSoundLabeler/
├── Views/                  # 用户界面
│   ├── MainWindow.xaml     # 主窗口界面
│   ├── MainWindow.xaml.cs  # 主窗口逻辑
│   ├── LabelingDialog.xaml # 标注对话框界面
│   └── LabelingDialog.xaml.cs # 标注对话框逻辑
├── Models/                 # 数据模型
│   ├── AppSettings.cs      # 应用程序设置
│   └── ProcessResult.cs    # 处理结果模型
├── Services/               # 业务服务
│   ├── ConfigService.cs    # 配置管理服务
│   ├── FileService.cs      # 文件操作服务
│   └── LogService.cs       # 日志记录服务
├── Styles/                 # 样式资源
│   ├── ButtonStyles.xaml   # 按钮样式
│   └── WindowStyles.xaml   # 窗口样式
├── Resources/              # 资源文件
│   └── app_icon.ico        # 应用程序图标
├── App.xaml               # 应用程序定义
├── App.xaml.cs            # 应用程序逻辑
└── BowelSoundLabeler.csproj # 项目文件
```

## 技术架构

### 开发技术
- **框架**：.NET 6.0
- **UI 技术**：WinUI 3 (Windows UI Library 3)
- **设计系统**：Fluent Design System
- **编程语言**：C# 10.0
- **设计模式**：MVVM、单例模式、服务模式

### 核心组件
- **ConfigService**：配置管理（单例模式）
- **FileService**：文件操作和验证
- **LogService**：日志记录和管理
- **MainWindow**：主用户界面
- **LabelingDialog**：标注对话框

### 数据存储
- **配置文件**：JSON 格式，存储在用户应用数据目录
- **日志文件**：文本格式，按日期自动分割
- **用户设置**：包括默认文件夹、窗口位置等

## 配置说明

### 应用程序设置
配置文件位置：`%APPDATA%\BowelSoundLabeler\appsettings.json`

主要配置项：
```json
{
  "DefaultFolder": "C:\\Users\\<USER>\\Documents",
  "WindowWidth": 700,
  "WindowHeight": 500,
  "EnableLogging": true,
  "LogLevel": "Info",
  "SupportedExtensions": [".mat", ".wav", ".mp3", ".m4a", ".flac"]
}
```

### 日志配置
- **日志目录**：`%APPDATA%\BowelSoundLabeler\Logs`
- **日志级别**：Debug, Info, Warning, Error, Fatal
- **文件分割**：按日期自动分割，单文件最大 10MB
- **保留期限**：默认保留 30 天

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查是否安装了 .NET 6.0 运行时
   - 检查 Windows 版本是否支持
   - 查看日志文件获取详细错误信息

2. **文件选择失败**
   - 检查文件是否被其他程序占用
   - 确认文件格式是否受支持
   - 检查文件路径是否包含特殊字符

3. **文件重命名失败**
   - 检查目标文件是否已存在
   - 确认对目录有写入权限
   - 检查磁盘空间是否充足

4. **设置无法保存**
   - 检查应用数据目录的写入权限
   - 确认磁盘空间充足
   - 查看日志文件获取详细错误信息

### 日志查看
1. 打开文件资源管理器
2. 在地址栏输入：`%APPDATA%\BowelSoundLabeler\Logs`
3. 打开最新的日志文件查看详细信息

## 开发说明

### 添加新功能
1. 在相应的文件夹中创建新的类文件
2. 遵循现有的命名规范和代码风格
3. 添加适当的日志记录和错误处理
4. 更新相关的测试用例

### 修改界面
1. 编辑对应的 XAML 文件
2. 在后台代码中添加事件处理逻辑
3. 更新样式文件以保持一致性
4. 测试不同分辨率下的显示效果

### 发布新版本
1. 更新版本号（在项目文件中）
2. 编译 Release 版本
3. 测试所有功能
4. 创建安装包或发布文件

## 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 联系方式

如有问题或建议，请联系开发团队：
- 邮箱：[开发者邮箱]
- 项目地址：[项目仓库地址]

---

**版本**：1.0.0  
**最后更新**：2025年8月22日
