using Microsoft.UI.Dispatching;
using Microsoft.UI.Xaml;
using Microsoft.Windows.AppLifecycle;
using System;
using System.Diagnostics;
using System.Threading.Tasks;
using Windows.ApplicationModel.Activation;
using WinRT;

namespace BowelSoundLabeler
{
    /// <summary>
    /// WinUI 3 应用程序入口点
    /// </summary>
    public static class Program
    {
        [STAThread]
        public static void Main(string[] args)
        {
            // 初始化 WinRT
            ComWrappersSupport.InitializeComWrappers();
            
            // 检查是否已有实例在运行
            var isRedirect = DecideRedirection();
            if (!isRedirect)
            {
                // 启动应用程序
                Application.Start((p) =>
                {
                    var context = new DispatcherQueueSynchronizationContext(
                        DispatcherQueue.GetForCurrentThread());
                    System.Threading.SynchronizationContext.SetSynchronizationContext(context);
                    
                    new App();
                });
            }
        }

        /// <summary>
        /// 决定是否重定向到已存在的实例
        /// </summary>
        /// <returns>是否重定向</returns>
        private static bool DecideRedirection()
        {
            try
            {
                var isRedirect = false;
                var args = AppInstance.GetCurrent().GetActivatedEventArgs();
                var keyInstance = AppInstance.FindOrRegisterForKey("BowelSoundLabeler_Main");

                if (keyInstance.IsCurrent)
                {
                    // 这是主实例
                    keyInstance.Activated += OnActivated;
                }
                else
                {
                    // 重定向到已存在的实例
                    isRedirect = true;
                    RedirectActivationToAsync(keyInstance, args).GetAwaiter().GetResult();
                }

                return isRedirect;
            }
            catch (Exception ex)
            {
                // 如果重定向失败，继续启动新实例
                Debug.WriteLine($"重定向失败: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 重定向激活到已存在的实例
        /// </summary>
        /// <param name="keyInstance">目标实例</param>
        /// <param name="args">激活参数</param>
        private static async Task RedirectActivationToAsync(AppInstance keyInstance, AppActivationArguments args)
        {
            try
            {
                await keyInstance.RedirectActivationToAsync(args);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"重定向激活失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理激活事件
        /// </summary>
        /// <param name="sender">发送者</param>
        /// <param name="e">激活事件参数</param>
        private static void OnActivated(object sender, AppActivationArguments e)
        {
            try
            {
                // 处理应用程序激活
                // 例如：将窗口带到前台
                var app = Application.Current as App;
                // 这里可以添加将窗口带到前台的逻辑
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"处理激活事件失败: {ex.Message}");
            }
        }
    }
}
