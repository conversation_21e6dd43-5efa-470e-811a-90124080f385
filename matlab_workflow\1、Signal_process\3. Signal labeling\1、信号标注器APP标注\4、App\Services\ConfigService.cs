using System;
using System.IO;
using Newtonsoft.Json;
using BowelSoundLabeler.Models;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 配置服务 - 单例模式
    /// </summary>
    public class ConfigService
    {
        private static ConfigService _instance;
        private static readonly object _lock = new object();
        
        private AppSettings _settings;
        private string _configFilePath;
        private readonly string _configFileName = "appsettings.json";

        /// <summary>
        /// 获取单例实例
        /// </summary>
        public static ConfigService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                            _instance = new ConfigService();
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// 私有构造函数
        /// </summary>
        private ConfigService()
        {
            InitializeConfigPath();
        }

        /// <summary>
        /// 初始化配置文件路径
        /// </summary>
        private void InitializeConfigPath()
        {
            try
            {
                var appDataPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                    "BowelSoundLabeler");

                if (!Directory.Exists(appDataPath))
                {
                    Directory.CreateDirectory(appDataPath);
                }

                _configFilePath = Path.Combine(appDataPath, _configFileName);
            }
            catch (Exception ex)
            {
                // 如果无法创建应用数据目录，使用程序目录
                LogService.Error($"无法创建应用数据目录，使用程序目录：{ex.Message}");
                _configFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, _configFileName);
            }
        }

        /// <summary>
        /// 初始化配置服务
        /// </summary>
        public void Initialize()
        {
            try
            {
                LoadSettings();
                LogService.Info("配置服务初始化完成");
            }
            catch (Exception ex)
            {
                LogService.Error($"配置服务初始化失败：{ex.Message}");
                _settings = new AppSettings();
            }
        }

        /// <summary>
        /// 加载设置
        /// </summary>
        private void LoadSettings()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = File.ReadAllText(_configFilePath);
                    _settings = JsonConvert.DeserializeObject<AppSettings>(json);
                    
                    // 验证设置有效性
                    if (_settings == null || !_settings.IsValid())
                    {
                        LogService.Warning("配置文件无效，使用默认设置");
                        _settings = new AppSettings();
                    }
                    else
                    {
                        // 清理无效的最近文件夹
                        _settings.CleanupRecentFolders();
                        LogService.Info("配置文件加载成功");
                    }
                }
                else
                {
                    _settings = new AppSettings();
                    LogService.Info("配置文件不存在，使用默认设置");
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"加载配置文件失败：{ex.Message}");
                _settings = new AppSettings();
            }
        }

        /// <summary>
        /// 保存设置
        /// </summary>
        public void SaveSettings()
        {
            try
            {
                if (_settings == null)
                {
                    _settings = new AppSettings();
                }

                _settings.LastUpdated = DateTime.Now;
                
                var json = JsonConvert.SerializeObject(_settings, Formatting.Indented);
                File.WriteAllText(_configFilePath, json);
                
                LogService.Info("配置文件保存成功");
            }
            catch (Exception ex)
            {
                LogService.Error($"保存配置文件失败：{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取默认文件夹
        /// </summary>
        /// <returns>默认文件夹路径</returns>
        public string GetDefaultFolder()
        {
            try
            {
                if (_settings?.DefaultFolder != null && Directory.Exists(_settings.DefaultFolder))
                {
                    return _settings.DefaultFolder;
                }
                
                // 如果默认文件夹不存在，返回用户文档目录
                return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }
            catch (Exception ex)
            {
                LogService.Error($"获取默认文件夹失败：{ex.Message}");
                return Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments);
            }
        }

        /// <summary>
        /// 设置默认文件夹
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        public void SetDefaultFolder(string folderPath)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(folderPath))
                {
                    throw new ArgumentException("文件夹路径不能为空");
                }

                if (!Directory.Exists(folderPath))
                {
                    throw new DirectoryNotFoundException($"文件夹不存在：{folderPath}");
                }

                if (_settings == null)
                {
                    _settings = new AppSettings();
                }

                _settings.DefaultFolder = folderPath;
                _settings.AddRecentFolder(folderPath);
                
                SaveSettings();
                
                LogService.Info($"默认文件夹已设置为：{folderPath}");
            }
            catch (Exception ex)
            {
                LogService.Error($"设置默认文件夹失败：{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 清除默认文件夹
        /// </summary>
        public void ClearDefaultFolder()
        {
            try
            {
                if (_settings == null)
                {
                    _settings = new AppSettings();
                }

                _settings.DefaultFolder = null;
                SaveSettings();
                
                LogService.Info("默认文件夹已清除");
            }
            catch (Exception ex)
            {
                LogService.Error($"清除默认文件夹失败：{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 获取应用程序设置
        /// </summary>
        /// <returns>应用程序设置</returns>
        public AppSettings GetSettings()
        {
            if (_settings == null)
            {
                _settings = new AppSettings();
            }
            return _settings;
        }

        /// <summary>
        /// 更新应用程序设置
        /// </summary>
        /// <param name="settings">新的设置</param>
        public void UpdateSettings(AppSettings settings)
        {
            try
            {
                if (settings == null)
                {
                    throw new ArgumentNullException(nameof(settings));
                }

                if (!settings.IsValid())
                {
                    throw new ArgumentException("设置无效");
                }

                _settings = settings;
                SaveSettings();
                
                LogService.Info("应用程序设置已更新");
            }
            catch (Exception ex)
            {
                LogService.Error($"更新应用程序设置失败：{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefaults()
        {
            try
            {
                if (_settings == null)
                {
                    _settings = new AppSettings();
                }
                else
                {
                    _settings.ResetToDefaults();
                }
                
                SaveSettings();
                
                LogService.Info("设置已重置为默认值");
            }
            catch (Exception ex)
            {
                LogService.Error($"重置设置失败：{ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// 备份配置文件
        /// </summary>
        /// <returns>备份文件路径</returns>
        public string BackupConfig()
        {
            try
            {
                if (!File.Exists(_configFilePath))
                {
                    throw new FileNotFoundException("配置文件不存在");
                }

                var backupPath = _configFilePath + $".backup.{DateTime.Now:yyyyMMddHHmmss}";
                File.Copy(_configFilePath, backupPath);
                
                LogService.Info($"配置文件已备份到：{backupPath}");
                return backupPath;
            }
            catch (Exception ex)
            {
                LogService.Error($"备份配置文件失败：{ex.Message}");
                throw;
            }
        }
    }
}
