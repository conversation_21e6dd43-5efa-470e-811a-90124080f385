<Window x:Class="BowelSoundLabeler.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="肠鸣音标注器">

    <Window.SystemBackdrop>
        <MicaBackdrop Kind="Base"/>
    </Window.SystemBackdrop>
    
    <Window.Resources>
        <!-- WinUI 3 窗口特定样式 -->
        <Style x:Key="MainButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="CornerRadius" Value="4"/>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0"
                   Text="肠鸣音标注器"
                   FontSize="24"
                   FontWeight="SemiBold"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,10"/>

        <!-- 说明文字 -->
        <TextBlock Grid.Row="1"
                   Text="选择音频/信号文件进行肠鸣音标注，系统将自动重命名文件"
                   FontSize="14"
                   HorizontalAlignment="Center"
                   TextWrapping="Wrap"
                   Margin="0,5,0,20"/>
        
        <!-- 默认文件夹设置区域 -->
        <StackPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Center" Spacing="10" Margin="0,0,0,15">
            <Button Name="SetDefaultFolderBtn"
                    Content="设置默认文件夹"
                    Style="{StaticResource MainButtonStyle}"
                    Click="SetDefaultFolderBtn_Click"/>

            <Button Name="ClearDefaultFolderBtn"
                    Content="清除默认文件夹"
                    Style="{StaticResource MainButtonStyle}"
                    Click="ClearDefaultFolderBtn_Click"/>
        </StackPanel>

        <!-- 当前默认文件夹显示 -->
        <TextBlock Grid.Row="3"
                   Name="CurrentFolderLabel"
                   Text="当前默认文件夹: 未设置"
                   FontSize="12"
                   HorizontalAlignment="Center"
                   Foreground="Gray"
                   Margin="0,0,0,15"/>
        
        <!-- 文件选择和列表区域 -->
        <Grid Grid.Row="4">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>

            <!-- 文件选择按钮 -->
            <StackPanel Grid.Row="0" Orientation="Horizontal" HorizontalAlignment="Center" Spacing="10" Margin="0,0,0,15">
                <Button Name="SelectSingleFileBtn"
                        Content="选择单个文件"
                        Style="{StaticResource MainButtonStyle}"
                        Click="SelectSingleFileBtn_Click"/>

                <Button Name="SelectMultipleFilesBtn"
                        Content="选择多个文件"
                        Style="{StaticResource MainButtonStyle}"
                        Click="SelectMultipleFilesBtn_Click"/>
            </StackPanel>

            <!-- 文件列表 -->
            <Border Grid.Row="1"
                    BorderBrush="Gray"
                    BorderThickness="1"
                    CornerRadius="8"
                    Background="{ThemeResource CardBackgroundFillColorDefaultBrush}">
                <ListView Name="FileListBox"
                          BorderThickness="0"
                          Margin="10">
                    <ListView.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}"
                                       FontSize="12"
                                       Margin="5,3"/>
                        </DataTemplate>
                    </ListView.ItemTemplate>
                </ListView>
            </Border>
        </Grid>
        
        <!-- 开始标注按钮 -->
        <Button Grid.Row="5"
                Name="StartLabelingBtn"
                Content="开始标注"
                Style="{StaticResource AccentButtonStyle}"
                Height="44"
                FontSize="14"
                FontWeight="SemiBold"
                Margin="0,20,0,15"
                IsEnabled="False"
                Click="StartLabelingBtn_Click"/>

        <!-- 状态栏 -->
        <TextBlock Grid.Row="6"
                   Name="StatusLabel"
                   Text="就绪"
                   FontSize="12"
                   HorizontalAlignment="Center"
                   Foreground="Gray"
                   Margin="0,0,0,10"/>

        <!-- 进度条 -->
        <ProgressBar Grid.Row="7"
                     Name="ProgressBar"
                     Height="6"
                     Margin="0,5,0,0"
                     Visibility="Collapsed"/>
    </Grid>
</Window>
