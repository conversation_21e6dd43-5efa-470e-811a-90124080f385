<Window x:Class="BowelSoundLabeler.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="肠鸣音标注器" 
        Height="500" 
        Width="700"
        MinHeight="400"
        MinWidth="600"
        ResizeMode="CanResize"
        WindowStartupLocation="CenterScreen">
    
    <Window.Resources>
        <!-- 窗口特定样式 -->
        <Style x:Key="MainButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontFamily" Value="{StaticResource DefaultFontFamily}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="肠鸣音标注器" 
                   Style="{StaticResource TitleTextStyle}"/>
        
        <!-- 说明文字 -->
        <TextBlock Grid.Row="1" 
                   Text="选择音频/信号文件进行肠鸣音标注，系统将自动重命名文件"
                   Style="{StaticResource NormalTextStyle}"
                   HorizontalAlignment="Center"
                   Margin="0,5,0,15"/>
        
        <!-- 默认文件夹设置区域 -->
        <Grid Grid.Row="2" Margin="0,0,0,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>
            
            <Button Grid.Column="0" 
                    Name="SetDefaultFolderBtn"
                    Content="设置默认文件夹"
                    Style="{StaticResource MainButtonStyle}"
                    Background="{StaticResource LightGrayBrush}"
                    Click="SetDefaultFolderBtn_Click"/>
            
            <Button Grid.Column="1" 
                    Name="ClearDefaultFolderBtn"
                    Content="清除默认文件夹"
                    Style="{StaticResource MainButtonStyle}"
                    Background="{StaticResource LightGrayBrush}"
                    Click="ClearDefaultFolderBtn_Click"/>
        </Grid>
        
        <!-- 当前默认文件夹显示 -->
        <TextBlock Grid.Row="3" 
                   Name="CurrentFolderLabel"
                   Text="当前默认文件夹: 未设置"
                   Style="{StaticResource StatusTextStyle}"
                   Margin="0,0,0,10"/>
        
        <!-- 文件选择和列表区域 -->
        <Grid Grid.Row="4">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- 文件选择按钮 -->
            <Grid Grid.Row="0" Margin="0,0,0,10">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>
                
                <Button Grid.Column="0" 
                        Name="SelectSingleFileBtn"
                        Content="选择单个文件"
                        Style="{StaticResource MainButtonStyle}"
                        Background="{StaticResource InfoBrush}"
                        Foreground="White"
                        Click="SelectSingleFileBtn_Click"/>
                
                <Button Grid.Column="1" 
                        Name="SelectMultipleFilesBtn"
                        Content="选择多个文件"
                        Style="{StaticResource MainButtonStyle}"
                        Background="{StaticResource InfoBrush}"
                        Foreground="White"
                        Click="SelectMultipleFilesBtn_Click"/>
            </Grid>
            
            <!-- 文件列表 -->
            <Border Grid.Row="1" 
                    BorderBrush="{StaticResource DarkGrayBrush}" 
                    BorderThickness="1" 
                    CornerRadius="4"
                    Background="White">
                <ListBox Name="FileListBox"
                         BorderThickness="0"
                         ScrollViewer.HorizontalScrollBarVisibility="Auto"
                         ScrollViewer.VerticalScrollBarVisibility="Auto">
                    <ListBox.ItemTemplate>
                        <DataTemplate>
                            <TextBlock Text="{Binding}" 
                                       FontFamily="{StaticResource DefaultFontFamily}"
                                       FontSize="11"
                                       Margin="5,2"/>
                        </DataTemplate>
                    </ListBox.ItemTemplate>
                </ListBox>
            </Border>
        </Grid>
        
        <!-- 开始标注按钮 -->
        <Button Grid.Row="5" 
                Name="StartLabelingBtn"
                Content="开始标注"
                Style="{StaticResource MainButtonStyle}"
                Background="{StaticResource PrimaryBrush}"
                Foreground="White"
                FontWeight="Bold"
                Height="40"
                Margin="0,15,0,10"
                IsEnabled="False"
                Click="StartLabelingBtn_Click"/>
        
        <!-- 状态栏 -->
        <TextBlock Grid.Row="6" 
                   Name="StatusLabel"
                   Text="就绪"
                   Style="{StaticResource StatusTextStyle}"/>
        
        <!-- 进度条 -->
        <ProgressBar Grid.Row="7" 
                     Name="ProgressBar"
                     Height="4"
                     Margin="0,5,0,0"
                     Visibility="Collapsed"/>
    </Grid>
</Window>
