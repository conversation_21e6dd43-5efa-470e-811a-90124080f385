using System;
using System.IO;
using System.Text;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 日志级别枚举
    /// </summary>
    public enum LogLevel
    {
        Debug = 0,
        Info = 1,
        Warning = 2,
        Error = 3,
        Fatal = 4
    }

    /// <summary>
    /// 日志服务 - 静态类
    /// </summary>
    public static class LogService
    {
        private static string _logDirectory;
        private static string _currentLogFile;
        private static readonly object _lockObject = new object();
        private static LogLevel _minLogLevel = LogLevel.Info;
        private static bool _isInitialized = false;

        /// <summary>
        /// 初始化日志服务
        /// </summary>
        /// <param name="logDirectory">日志目录</param>
        /// <param name="minLogLevel">最小日志级别</param>
        public static void Initialize(string logDirectory, LogLevel minLogLevel = LogLevel.Info)
        {
            try
            {
                _logDirectory = logDirectory;
                _minLogLevel = minLogLevel;

                if (!Directory.Exists(_logDirectory))
                {
                    Directory.CreateDirectory(_logDirectory);
                }

                _currentLogFile = Path.Combine(_logDirectory, $"app_{DateTime.Now:yyyyMMdd}.log");
                _isInitialized = true;

                // 清理旧日志文件
                CleanupOldLogFiles();

                Info("日志服务初始化完成");
            }
            catch (Exception ex)
            {
                // 如果日志初始化失败，至少要能记录到控制台
                Console.WriteLine($"日志服务初始化失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 记录调试信息
        /// </summary>
        /// <param name="message">消息</param>
        public static void Debug(string message)
        {
            WriteLog(LogLevel.Debug, message);
        }

        /// <summary>
        /// 记录信息
        /// </summary>
        /// <param name="message">消息</param>
        public static void Info(string message)
        {
            WriteLog(LogLevel.Info, message);
        }

        /// <summary>
        /// 记录警告
        /// </summary>
        /// <param name="message">消息</param>
        public static void Warning(string message)
        {
            WriteLog(LogLevel.Warning, message);
        }

        /// <summary>
        /// 记录错误
        /// </summary>
        /// <param name="message">消息</param>
        public static void Error(string message)
        {
            WriteLog(LogLevel.Error, message);
        }

        /// <summary>
        /// 记录严重错误
        /// </summary>
        /// <param name="message">消息</param>
        public static void Fatal(string message)
        {
            WriteLog(LogLevel.Fatal, message);
        }

        /// <summary>
        /// 记录异常
        /// </summary>
        /// <param name="exception">异常对象</param>
        /// <param name="message">附加消息</param>
        public static void Exception(Exception exception, string message = null)
        {
            var logMessage = string.IsNullOrEmpty(message) 
                ? $"异常：{exception}" 
                : $"{message} - 异常：{exception}";
            
            WriteLog(LogLevel.Error, logMessage);
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        /// <param name="level">日志级别</param>
        /// <param name="message">消息</param>
        private static void WriteLog(LogLevel level, string message)
        {
            if (!_isInitialized || level < _minLogLevel)
                return;

            try
            {
                lock (_lockObject)
                {
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    var levelString = level.ToString().ToUpper().PadRight(7);
                    var threadId = System.Threading.Thread.CurrentThread.ManagedThreadId;
                    
                    var logEntry = $"[{timestamp}] [{levelString}] [Thread-{threadId:D2}] {message}";

                    // 写入文件
                    WriteToFile(logEntry);

                    // 如果是错误或严重错误，同时输出到控制台
                    if (level >= LogLevel.Error)
                    {
                        Console.WriteLine(logEntry);
                    }
                }
            }
            catch (Exception ex)
            {
                // 日志写入失败时，至少输出到控制台
                Console.WriteLine($"日志写入失败：{ex.Message}");
                Console.WriteLine($"原始消息：{message}");
            }
        }

        /// <summary>
        /// 写入文件
        /// </summary>
        /// <param name="logEntry">日志条目</param>
        private static void WriteToFile(string logEntry)
        {
            try
            {
                // 检查是否需要创建新的日志文件（按日期分割）
                var today = DateTime.Now.ToString("yyyyMMdd");
                var expectedLogFile = Path.Combine(_logDirectory, $"app_{today}.log");
                
                if (_currentLogFile != expectedLogFile)
                {
                    _currentLogFile = expectedLogFile;
                }

                // 检查文件大小，如果超过限制则创建新文件
                if (File.Exists(_currentLogFile))
                {
                    var fileInfo = new FileInfo(_currentLogFile);
                    if (fileInfo.Length > 10 * 1024 * 1024) // 10MB
                    {
                        var timestamp = DateTime.Now.ToString("HHmmss");
                        _currentLogFile = Path.Combine(_logDirectory, $"app_{today}_{timestamp}.log");
                    }
                }

                // 写入日志
                using (var writer = new StreamWriter(_currentLogFile, true, Encoding.UTF8))
                {
                    writer.WriteLine(logEntry);
                    writer.Flush();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"写入日志文件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 清理旧日志文件
        /// </summary>
        private static void CleanupOldLogFiles()
        {
            try
            {
                if (!Directory.Exists(_logDirectory))
                    return;

                var cutoffDate = DateTime.Now.AddDays(-30); // 保留30天的日志
                var logFiles = Directory.GetFiles(_logDirectory, "app_*.log");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        try
                        {
                            File.Delete(logFile);
                            Console.WriteLine($"已删除旧日志文件：{logFile}");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"删除旧日志文件失败：{logFile} - {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"清理旧日志文件失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取当前日志文件路径
        /// </summary>
        /// <returns>日志文件路径</returns>
        public static string GetCurrentLogFile()
        {
            return _currentLogFile;
        }

        /// <summary>
        /// 获取日志目录
        /// </summary>
        /// <returns>日志目录</returns>
        public static string GetLogDirectory()
        {
            return _logDirectory;
        }

        /// <summary>
        /// 设置最小日志级别
        /// </summary>
        /// <param name="minLogLevel">最小日志级别</param>
        public static void SetMinLogLevel(LogLevel minLogLevel)
        {
            _minLogLevel = minLogLevel;
            Info($"日志级别已设置为：{minLogLevel}");
        }

        /// <summary>
        /// 强制刷新日志缓冲区
        /// </summary>
        public static void Flush()
        {
            // 由于使用了using语句，每次写入都会自动刷新
            // 这个方法主要用于接口兼容性
            Info("日志缓冲区已刷新");
        }

        /// <summary>
        /// 获取日志统计信息
        /// </summary>
        /// <returns>日志统计信息</returns>
        public static string GetLogStatistics()
        {
            try
            {
                if (!Directory.Exists(_logDirectory))
                    return "日志目录不存在";

                var logFiles = Directory.GetFiles(_logDirectory, "app_*.log");
                var totalSize = 0L;
                var fileCount = logFiles.Length;

                foreach (var file in logFiles)
                {
                    var fileInfo = new FileInfo(file);
                    totalSize += fileInfo.Length;
                }

                var totalSizeMB = totalSize / (1024.0 * 1024.0);

                return $"日志文件数量：{fileCount}，总大小：{totalSizeMB:F2} MB";
            }
            catch (Exception ex)
            {
                return $"获取日志统计信息失败：{ex.Message}";
            }
        }
    }
}
