using System;
using System.Windows;
using System.Windows.Input;
using BowelSoundLabeler.Services;

namespace BowelSoundLabeler.Views
{
    /// <summary>
    /// 标注对话框交互逻辑
    /// </summary>
    public partial class LabelingDialog : Window
    {
        /// <summary>
        /// 是否包含肠鸣音
        /// </summary>
        public bool HasBowelSound { get; private set; }

        /// <summary>
        /// 肠鸣音数量
        /// </summary>
        public int BowelSoundCount { get; private set; }

        /// <summary>
        /// 是否已选择
        /// </summary>
        private bool _hasSelected;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="fileName">文件名</param>
        public LabelingDialog(string fileName)
        {
            InitializeComponent();
            
            FileNameLabel.Text = $"文件: {fileName}";
            BowelSoundCount = 1;
            _hasSelected = false;
            
            // 设置焦点到第一个按钮
            YesBtn.Focus();
            
            // 添加键盘快捷键支持
            KeyDown += LabelingDialog_KeyDown;
            
            LogService.Info($"打开标注对话框：{fileName}");
        }

        /// <summary>
        /// 键盘快捷键处理
        /// </summary>
        private void LabelingDialog_KeyDown(object sender, KeyEventArgs e)
        {
            switch (e.Key)
            {
                case Key.Y:
                    if (!_hasSelected)
                        YesBtn_Click(null, null);
                    break;
                case Key.N:
                    if (!_hasSelected)
                        NoBtn_Click(null, null);
                    break;
                case Key.Enter:
                    if (ConfirmBtn.IsEnabled)
                        ConfirmBtn_Click(null, null);
                    break;
                case Key.Escape:
                    CancelBtn_Click(null, null);
                    break;
                case Key.S:
                    SkipBtn_Click(null, null);
                    break;
            }
        }

        /// <summary>
        /// "是"按钮点击事件
        /// </summary>
        private void YesBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                HasBowelSound = true;
                _hasSelected = true;
                
                // 显示数量输入区域
                CountInputPanel.Visibility = Visibility.Visible;
                ConfirmBtn.IsEnabled = true;
                
                // 禁用选择按钮
                YesBtn.IsEnabled = false;
                NoBtn.IsEnabled = false;
                
                // 设置焦点到数量输入框
                CountTextBox.Focus();
                CountTextBox.SelectAll();
                
                LogService.Info("用户选择：包含肠鸣音");
            }
            catch (Exception ex)
            {
                LogService.Error($"处理'是'按钮点击时发生错误：{ex.Message}");
                ShowErrorMessage("操作失败", ex.Message);
            }
        }

        /// <summary>
        /// "否"按钮点击事件
        /// </summary>
        private void NoBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                HasBowelSound = false;
                BowelSoundCount = 0;
                _hasSelected = true;
                
                // 隐藏数量输入区域
                CountInputPanel.Visibility = Visibility.Collapsed;
                ConfirmBtn.IsEnabled = true;
                
                // 禁用选择按钮
                YesBtn.IsEnabled = false;
                NoBtn.IsEnabled = false;
                
                // 设置焦点到确定按钮
                ConfirmBtn.Focus();
                
                LogService.Info("用户选择：不包含肠鸣音");
            }
            catch (Exception ex)
            {
                LogService.Error($"处理'否'按钮点击时发生错误：{ex.Message}");
                ShowErrorMessage("操作失败", ex.Message);
            }
        }

        /// <summary>
        /// 数量文本框内容变化事件
        /// </summary>
        private void CountTextBox_TextChanged(object sender, System.Windows.Controls.TextChangedEventArgs e)
        {
            try
            {
                var text = CountTextBox.Text.Trim();
                
                if (string.IsNullOrEmpty(text))
                {
                    ShowCountError("请输入数量");
                    ConfirmBtn.IsEnabled = false;
                    return;
                }
                
                if (int.TryParse(text, out int count))
                {
                    if (count > 0)
                    {
                        BowelSoundCount = count;
                        HideCountError();
                        ConfirmBtn.IsEnabled = true;
                    }
                    else
                    {
                        ShowCountError("数量必须大于0");
                        ConfirmBtn.IsEnabled = false;
                    }
                }
                else
                {
                    ShowCountError("请输入有效的正整数");
                    ConfirmBtn.IsEnabled = false;
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"处理数量输入时发生错误：{ex.Message}");
                ShowCountError("输入处理错误");
                ConfirmBtn.IsEnabled = false;
            }
        }

        /// <summary>
        /// 确定按钮点击事件
        /// </summary>
        private void ConfirmBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!_hasSelected)
                {
                    ShowErrorMessage("请先选择是否包含肠鸣音");
                    return;
                }
                
                if (HasBowelSound && BowelSoundCount <= 0)
                {
                    ShowErrorMessage("请输入有效的肠鸣音数量");
                    CountTextBox.Focus();
                    return;
                }
                
                LogService.Info($"标注确认 - 包含肠鸣音：{HasBowelSound}, 数量：{BowelSoundCount}");
                
                DialogResult = true;
                Close();
            }
            catch (Exception ex)
            {
                LogService.Error($"确认标注时发生错误：{ex.Message}");
                ShowErrorMessage("确认失败", ex.Message);
            }
        }

        /// <summary>
        /// 跳过按钮点击事件
        /// </summary>
        private void SkipBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                LogService.Info("用户选择跳过当前文件");
                
                DialogResult = false;
                Close();
            }
            catch (Exception ex)
            {
                LogService.Error($"跳过操作时发生错误：{ex.Message}");
                ShowErrorMessage("操作失败", ex.Message);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelBtn_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    "确定要取消标注过程吗？",
                    "确认取消",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    LogService.Info("用户取消标注过程");
                    
                    DialogResult = null;
                    Close();
                }
            }
            catch (Exception ex)
            {
                LogService.Error($"取消操作时发生错误：{ex.Message}");
                ShowErrorMessage("操作失败", ex.Message);
            }
        }

        /// <summary>
        /// 显示数量输入错误
        /// </summary>
        private void ShowCountError(string message)
        {
            CountErrorLabel.Text = message;
            CountErrorLabel.Visibility = Visibility.Visible;
        }

        /// <summary>
        /// 隐藏数量输入错误
        /// </summary>
        private void HideCountError()
        {
            CountErrorLabel.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 显示错误消息
        /// </summary>
        private void ShowErrorMessage(string message)
        {
            MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }

        /// <summary>
        /// 显示错误消息（带标题）
        /// </summary>
        private void ShowErrorMessage(string title, string message)
        {
            MessageBox.Show(message, title, MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
