@echo off
chcp 65001 >nul
echo ========================================
echo 肠鸣音标注器 - 构建脚本
echo ========================================
echo.

:: 设置变量
set PROJECT_NAME=BowelSoundLabeler
set PROJECT_FILE=%PROJECT_NAME%.csproj
set OUTPUT_DIR=bin\Release\net6.0-windows\win-x64\publish
set DIST_DIR=dist

:: 检查项目文件是否存在
if not exist "%PROJECT_FILE%" (
    echo 错误：找不到项目文件 %PROJECT_FILE%
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

:: 检查 .NET SDK
echo 检查 .NET SDK...
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到 .NET SDK
    echo 请安装 .NET 6.0 SDK 或更高版本
    pause
    exit /b 1
)

echo 找到 .NET SDK 版本：
dotnet --version
echo.

:: 清理之前的构建
echo 清理之前的构建文件...
if exist "bin" rmdir /s /q "bin"
if exist "obj" rmdir /s /q "obj"
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
echo 清理完成
echo.

:: 还原 NuGet 包
echo 还原 NuGet 包...
dotnet restore
if errorlevel 1 (
    echo 错误：NuGet 包还原失败
    pause
    exit /b 1
)
echo NuGet 包还原完成
echo.

:: 构建项目
echo 构建项目...
dotnet build -c Release
if errorlevel 1 (
    echo 错误：项目构建失败
    pause
    exit /b 1
)
echo 项目构建完成
echo.

:: 发布应用程序
echo 发布应用程序...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true -p:IncludeNativeLibrariesForSelfExtract=true
if errorlevel 1 (
    echo 错误：应用程序发布失败
    pause
    exit /b 1
)
echo 应用程序发布完成
echo.

:: 创建分发目录
echo 创建分发目录...
mkdir "%DIST_DIR%" 2>nul

:: 复制可执行文件
if exist "%OUTPUT_DIR%\%PROJECT_NAME%.exe" (
    copy "%OUTPUT_DIR%\%PROJECT_NAME%.exe" "%DIST_DIR%\"
    echo 已复制可执行文件到 %DIST_DIR%
) else (
    echo 警告：未找到可执行文件 %OUTPUT_DIR%\%PROJECT_NAME%.exe
)

:: 复制说明文件
if exist "README.md" (
    copy "README.md" "%DIST_DIR%\"
    echo 已复制 README.md
)

:: 创建版本信息文件
echo 创建版本信息文件...
echo 肠鸣音标注器 v1.0.0 > "%DIST_DIR%\VERSION.txt"
echo 构建时间：%date% %time% >> "%DIST_DIR%\VERSION.txt"
echo 构建环境：Windows >> "%DIST_DIR%\VERSION.txt"
echo .NET 版本：>> "%DIST_DIR%\VERSION.txt"
dotnet --version >> "%DIST_DIR%\VERSION.txt"

:: 显示文件信息
echo.
echo ========================================
echo 构建完成！
echo ========================================
echo.
echo 输出目录：%DIST_DIR%
echo.
echo 文件列表：
dir "%DIST_DIR%" /b
echo.

:: 显示文件大小
if exist "%DIST_DIR%\%PROJECT_NAME%.exe" (
    for %%F in ("%DIST_DIR%\%PROJECT_NAME%.exe") do (
        echo 可执行文件大小：%%~zF 字节 ^(%.2f MB^)
        set /a size_mb=%%~zF/1024/1024
    )
)

echo.
echo 构建成功！可执行文件位于：%DIST_DIR%\%PROJECT_NAME%.exe
echo.
echo 按任意键退出...
pause >nul
