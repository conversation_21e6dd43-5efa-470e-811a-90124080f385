<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- 现代化窗口样式 -->
    <Style x:Key="ModernWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="#FAFAFA"/>
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="TextOptions.TextFormattingMode" Value="Display"/>
        <Setter Property="TextOptions.TextRenderingMode" Value="ClearType"/>
        <Setter Property="WindowStyle" Value="SingleBorderWindow"/>
        <Setter Property="AllowsTransparency" Value="False"/>
        <Setter Property="ResizeMode" Value="CanResize"/>
    </Style>

    <!-- 对话框窗口样式 -->
    <Style x:Key="DialogWindowStyle" TargetType="Window" BasedOn="{StaticResource ModernWindowStyle}">
        <Setter Property="ResizeMode" Value="NoResize"/>
        <Setter Property="ShowInTaskbar" Value="False"/>
        <Setter Property="WindowStartupLocation" Value="CenterOwner"/>
        <Setter Property="SizeToContent" Value="WidthAndHeight"/>
    </Style>

    <!-- 文本框样式 -->
    <Style x:Key="ModernTextBoxStyle" TargetType="TextBox">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="SelectionBrush" Value="#4CAF50"/>
        <Setter Property="CaretBrush" Value="Black"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TextBox">
                    <Border Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4"
                            Padding="{TemplateBinding Padding}">
                        <ScrollViewer Name="PART_ContentHost"
                                    Focusable="False"
                                    HorizontalScrollBarVisibility="Hidden"
                                    VerticalScrollBarVisibility="Hidden"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="#999999"/>
                        </Trigger>
                        <Trigger Property="IsFocused" Value="True">
                            <Setter TargetName="Border" Property="BorderBrush" Value="#4CAF50"/>
                            <Setter TargetName="Border" Property="BorderThickness" Value="2"/>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter TargetName="Border" Property="Background" Value="#F5F5F5"/>
                            <Setter TargetName="Border" Property="BorderBrush" Value="#E0E0E0"/>
                            <Setter Property="Foreground" Value="#999999"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 列表框样式 -->
    <Style x:Key="ModernListBoxStyle" TargetType="ListBox">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBox">
                    <Border Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="4">
                        <ScrollViewer Name="PART_ScrollViewer"
                                    Focusable="False"
                                    Padding="2">
                            <ItemsPresenter/>
                        </ScrollViewer>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 列表框项样式 -->
    <Style x:Key="ModernListBoxItemStyle" TargetType="ListBoxItem">
        <Setter Property="Padding" Value="8,4"/>
        <Setter Property="Margin" Value="1"/>
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
        <Setter Property="VerticalContentAlignment" Value="Center"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ListBoxItem">
                    <Border Name="Border"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="2"
                            Padding="{TemplateBinding Padding}">
                        <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                        VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#E8F5E8"/>
                        </Trigger>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter TargetName="Border" Property="Background" Value="#C8E6C9"/>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="True"/>
                                <Condition Property="IsMouseOver" Value="True"/>
                            </MultiTrigger.Conditions>
                            <Setter TargetName="Border" Property="Background" Value="#A5D6A7"/>
                        </MultiTrigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 进度条样式 -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar">
        <Setter Property="Height" Value="6"/>
        <Setter Property="Background" Value="#E0E0E0"/>
        <Setter Property="Foreground" Value="#4CAF50"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ProgressBar">
                    <Border Name="PART_Track"
                            Background="{TemplateBinding Background}"
                            CornerRadius="3">
                        <Border Name="PART_Indicator"
                                Background="{TemplateBinding Foreground}"
                                CornerRadius="3"
                                HorizontalAlignment="Left"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 标签样式 -->
    <Style x:Key="HeaderLabelStyle" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="#2E7D32"/>
        <Setter Property="Margin" Value="0,0,0,10"/>
    </Style>

    <Style x:Key="SubHeaderLabelStyle" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <Setter Property="Foreground" Value="#424242"/>
        <Setter Property="Margin" Value="0,0,0,5"/>
    </Style>

    <Style x:Key="NormalLabelStyle" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="Black"/>
    </Style>

    <Style x:Key="StatusLabelStyle" TargetType="Label">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="11"/>
        <Setter Property="FontWeight" Value="Normal"/>
        <Setter Property="Foreground" Value="#757575"/>
        <Setter Property="FontStyle" Value="Italic"/>
    </Style>

    <!-- 分组框样式 -->
    <Style x:Key="ModernGroupBoxStyle" TargetType="GroupBox">
        <Setter Property="FontFamily" Value="Microsoft YaHei UI, Segoe UI, Arial"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Setter Property="Foreground" Value="#2E7D32"/>
        <Setter Property="BorderBrush" Value="#CCCCCC"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="5"/>
        <Setter Property="Padding" Value="10"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>
                        
                        <Border Grid.Row="0"
                                Background="White"
                                Margin="10,0,0,0"
                                Padding="5,0">
                            <ContentPresenter ContentSource="Header"
                                            TextBlock.FontWeight="{TemplateBinding FontWeight}"
                                            TextBlock.FontSize="{TemplateBinding FontSize}"
                                            TextBlock.Foreground="{TemplateBinding Foreground}"/>
                        </Border>
                        
                        <Border Grid.Row="1"
                                Background="White"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>
