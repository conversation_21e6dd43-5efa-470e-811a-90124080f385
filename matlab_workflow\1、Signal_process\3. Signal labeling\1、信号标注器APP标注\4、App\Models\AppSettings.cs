using System;
using System.Collections.Generic;

namespace BowelSoundLabeler.Models
{
    /// <summary>
    /// 应用程序设置
    /// </summary>
    public class AppSettings
    {
        /// <summary>
        /// 默认工作文件夹
        /// </summary>
        public string DefaultFolder { get; set; }

        /// <summary>
        /// 窗口位置X
        /// </summary>
        public double WindowLeft { get; set; }

        /// <summary>
        /// 窗口位置Y
        /// </summary>
        public double WindowTop { get; set; }

        /// <summary>
        /// 窗口宽度
        /// </summary>
        public double WindowWidth { get; set; }

        /// <summary>
        /// 窗口高度
        /// </summary>
        public double WindowHeight { get; set; }

        /// <summary>
        /// 窗口状态
        /// </summary>
        public System.Windows.WindowState WindowState { get; set; }

        /// <summary>
        /// 支持的文件扩展名
        /// </summary>
        public List<string> SupportedExtensions { get; set; }

        /// <summary>
        /// 是否启用日志记录
        /// </summary>
        public bool EnableLogging { get; set; }

        /// <summary>
        /// 日志级别
        /// </summary>
        public string LogLevel { get; set; }

        /// <summary>
        /// 最大日志文件大小（MB）
        /// </summary>
        public int MaxLogFileSizeMB { get; set; }

        /// <summary>
        /// 保留日志文件天数
        /// </summary>
        public int LogRetentionDays { get; set; }

        /// <summary>
        /// 是否在文件存在时自动覆盖
        /// </summary>
        public bool AutoOverwriteExistingFiles { get; set; }

        /// <summary>
        /// 是否显示处理进度
        /// </summary>
        public bool ShowProgress { get; set; }

        /// <summary>
        /// 最近使用的文件夹列表
        /// </summary>
        public List<string> RecentFolders { get; set; }

        /// <summary>
        /// 最大最近文件夹数量
        /// </summary>
        public int MaxRecentFolders { get; set; }

        /// <summary>
        /// 应用程序版本
        /// </summary>
        public string AppVersion { get; set; }

        /// <summary>
        /// 设置最后更新时间
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// 构造函数 - 设置默认值
        /// </summary>
        public AppSettings()
        {
            // 窗口设置默认值
            WindowLeft = 100;
            WindowTop = 100;
            WindowWidth = 700;
            WindowHeight = 500;
            WindowState = System.Windows.WindowState.Normal;

            // 文件设置默认值
            SupportedExtensions = new List<string> 
            { 
                ".mat", ".wav", ".mp3", ".m4a", ".flac" 
            };

            // 日志设置默认值
            EnableLogging = true;
            LogLevel = "Info";
            MaxLogFileSizeMB = 10;
            LogRetentionDays = 30;

            // 行为设置默认值
            AutoOverwriteExistingFiles = false;
            ShowProgress = true;

            // 最近文件夹设置
            RecentFolders = new List<string>();
            MaxRecentFolders = 10;

            // 应用程序信息
            AppVersion = "1.0.0";
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// 验证设置的有效性
        /// </summary>
        /// <returns>验证结果</returns>
        public bool IsValid()
        {
            try
            {
                // 验证窗口尺寸
                if (WindowWidth < 400 || WindowHeight < 300)
                    return false;

                // 验证日志设置
                if (MaxLogFileSizeMB < 1 || MaxLogFileSizeMB > 100)
                    return false;

                if (LogRetentionDays < 1 || LogRetentionDays > 365)
                    return false;

                // 验证最近文件夹数量
                if (MaxRecentFolders < 1 || MaxRecentFolders > 50)
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 重置为默认设置
        /// </summary>
        public void ResetToDefaults()
        {
            var defaultSettings = new AppSettings();
            
            // 保留用户的文件夹设置
            var currentDefaultFolder = DefaultFolder;
            var currentRecentFolders = new List<string>(RecentFolders ?? new List<string>());

            // 复制所有默认值
            WindowLeft = defaultSettings.WindowLeft;
            WindowTop = defaultSettings.WindowTop;
            WindowWidth = defaultSettings.WindowWidth;
            WindowHeight = defaultSettings.WindowHeight;
            WindowState = defaultSettings.WindowState;
            SupportedExtensions = new List<string>(defaultSettings.SupportedExtensions);
            EnableLogging = defaultSettings.EnableLogging;
            LogLevel = defaultSettings.LogLevel;
            MaxLogFileSizeMB = defaultSettings.MaxLogFileSizeMB;
            LogRetentionDays = defaultSettings.LogRetentionDays;
            AutoOverwriteExistingFiles = defaultSettings.AutoOverwriteExistingFiles;
            ShowProgress = defaultSettings.ShowProgress;
            MaxRecentFolders = defaultSettings.MaxRecentFolders;
            AppVersion = defaultSettings.AppVersion;

            // 恢复用户的文件夹设置
            DefaultFolder = currentDefaultFolder;
            RecentFolders = currentRecentFolders;
            
            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// 添加最近使用的文件夹
        /// </summary>
        /// <param name="folderPath">文件夹路径</param>
        public void AddRecentFolder(string folderPath)
        {
            if (string.IsNullOrWhiteSpace(folderPath))
                return;

            if (RecentFolders == null)
                RecentFolders = new List<string>();

            // 移除已存在的相同路径
            RecentFolders.RemoveAll(f => string.Equals(f, folderPath, StringComparison.OrdinalIgnoreCase));

            // 添加到列表开头
            RecentFolders.Insert(0, folderPath);

            // 限制列表长度
            while (RecentFolders.Count > MaxRecentFolders)
            {
                RecentFolders.RemoveAt(RecentFolders.Count - 1);
            }

            LastUpdated = DateTime.Now;
        }

        /// <summary>
        /// 清理无效的最近文件夹
        /// </summary>
        public void CleanupRecentFolders()
        {
            if (RecentFolders == null)
                return;

            RecentFolders.RemoveAll(folder => 
                string.IsNullOrWhiteSpace(folder) || 
                !System.IO.Directory.Exists(folder));

            LastUpdated = DateTime.Now;
        }
    }
}
