<Window x:Class="BowelSoundLabeler.Views.LabelingDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="肠鸣音标注" 
        Height="300" 
        Width="450"
        ResizeMode="NoResize"
        WindowStartupLocation="CenterOwner"
        ShowInTaskbar="False"
        Icon="../Resources/app_icon.ico">
    
    <Window.Resources>
        <Style x:Key="DialogButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Width" Value="80"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontFamily" Value="{StaticResource DefaultFontFamily}"/>
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}" 
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="1"
                                CornerRadius="4">
                            <ContentPresenter HorizontalAlignment="Center" 
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Opacity" Value="0.6"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 文件名显示 -->
        <TextBlock Grid.Row="0" 
                   Name="FileNameLabel"
                   Text="文件: example.wav"
                   Style="{StaticResource NormalTextStyle}"
                   FontWeight="Bold"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>
        
        <!-- 问题 -->
        <TextBlock Grid.Row="1" 
                   Text="此文件是否包含肠鸣音？"
                   Style="{StaticResource NormalTextStyle}"
                   FontSize="14"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,20"/>
        
        <!-- 选择按钮 -->
        <StackPanel Grid.Row="2" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center"
                    Margin="0,0,0,20">
            <Button Name="YesBtn"
                    Content="是"
                    Style="{StaticResource DialogButtonStyle}"
                    Background="{StaticResource AccentBrush}"
                    Foreground="White"
                    Click="YesBtn_Click"/>
            
            <Button Name="NoBtn"
                    Content="否"
                    Style="{StaticResource DialogButtonStyle}"
                    Background="{StaticResource DangerBrush}"
                    Foreground="White"
                    Click="NoBtn_Click"/>
        </StackPanel>
        
        <!-- 数量输入区域 -->
        <StackPanel Grid.Row="3" 
                    Name="CountInputPanel"
                    Orientation="Vertical"
                    HorizontalAlignment="Center"
                    Visibility="Collapsed"
                    Margin="0,0,0,20">
            
            <TextBlock Text="请输入肠鸣音的数量："
                       Style="{StaticResource NormalTextStyle}"
                       HorizontalAlignment="Center"
                       Margin="0,0,0,10"/>
            
            <TextBox Name="CountTextBox"
                     Width="100"
                     Height="25"
                     FontFamily="{StaticResource DefaultFontFamily}"
                     FontSize="12"
                     Text="1"
                     HorizontalContentAlignment="Center"
                     VerticalContentAlignment="Center"
                     TextChanged="CountTextBox_TextChanged"/>
            
            <TextBlock Name="CountErrorLabel"
                       Text="请输入有效的正整数"
                       Style="{StaticResource StatusTextStyle}"
                       Foreground="{StaticResource DangerBrush}"
                       HorizontalAlignment="Center"
                       Visibility="Collapsed"
                       Margin="0,5,0,0"/>
        </StackPanel>
        
        <!-- 占位空间 -->
        <Grid Grid.Row="4"/>
        
        <!-- 底部按钮 -->
        <StackPanel Grid.Row="5" 
                    Orientation="Horizontal" 
                    HorizontalAlignment="Center">
            <Button Name="ConfirmBtn"
                    Content="确定"
                    Style="{StaticResource DialogButtonStyle}"
                    Background="{StaticResource PrimaryBrush}"
                    Foreground="White"
                    IsEnabled="False"
                    Click="ConfirmBtn_Click"/>
            
            <Button Name="SkipBtn"
                    Content="跳过"
                    Style="{StaticResource DialogButtonStyle}"
                    Background="{StaticResource WarningBrush}"
                    Foreground="White"
                    Click="SkipBtn_Click"/>
            
            <Button Name="CancelBtn"
                    Content="取消"
                    Style="{StaticResource DialogButtonStyle}"
                    Background="{StaticResource LightGrayBrush}"
                    Click="CancelBtn_Click"/>
        </StackPanel>
    </Grid>
</Window>
