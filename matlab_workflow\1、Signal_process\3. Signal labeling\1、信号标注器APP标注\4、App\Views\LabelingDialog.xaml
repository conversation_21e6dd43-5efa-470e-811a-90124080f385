<Window x:Class="BowelSoundLabeler.Views.LabelingDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="肠鸣音标注">

    <Window.SystemBackdrop>
        <MicaBackdrop Kind="BaseAlt"/>
    </Window.SystemBackdrop>
    
    <Window.Resources>
        <Style x:Key="DialogButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="36"/>
            <Setter Property="MinWidth" Value="88"/>
            <Setter Property="Margin" Value="6"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="CornerRadius" Value="4"/>
        </Style>
    </Window.Resources>

    <Grid Margin="24" MinWidth="400" MinHeight="280">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 文件名显示 -->
        <TextBlock Grid.Row="0"
                   Name="FileNameLabel"
                   Text="文件: example.wav"
                   FontSize="16"
                   FontWeight="SemiBold"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,24"/>

        <!-- 问题 -->
        <TextBlock Grid.Row="1"
                   Text="此文件是否包含肠鸣音？"
                   FontSize="14"
                   HorizontalAlignment="Center"
                   Margin="0,0,0,24"/>

        <!-- 选择按钮 -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Spacing="12"
                    Margin="0,0,0,24">
            <Button Name="YesBtn"
                    Content="是"
                    Style="{StaticResource AccentButtonStyle}"
                    Click="YesBtn_Click"/>

            <Button Name="NoBtn"
                    Content="否"
                    Click="NoBtn_Click"/>
        </StackPanel>
        
        <!-- 数量输入区域 -->
        <StackPanel Grid.Row="3"
                    Name="CountInputPanel"
                    Orientation="Vertical"
                    HorizontalAlignment="Center"
                    Visibility="Collapsed"
                    Spacing="12"
                    Margin="0,0,0,24">

            <TextBlock Text="请输入肠鸣音的数量："
                       FontSize="14"
                       HorizontalAlignment="Center"/>

            <NumberBox Name="CountNumberBox"
                       Width="120"
                       Value="1"
                       Minimum="1"
                       Maximum="999"
                       SpinButtonPlacementMode="Inline"
                       ValueChanged="CountNumberBox_ValueChanged"/>

            <TextBlock Name="CountErrorLabel"
                       Text="请输入有效的正整数"
                       FontSize="12"
                       Foreground="Red"
                       HorizontalAlignment="Center"
                       Visibility="Collapsed"/>
        </StackPanel>

        <!-- 占位空间 -->
        <Grid Grid.Row="4"/>

        <!-- 底部按钮 -->
        <StackPanel Grid.Row="5"
                    Orientation="Horizontal"
                    HorizontalAlignment="Center"
                    Spacing="12">
            <Button Name="ConfirmBtn"
                    Content="确定"
                    Style="{StaticResource AccentButtonStyle}"
                    IsEnabled="False"
                    Click="ConfirmBtn_Click"/>

            <Button Name="SkipBtn"
                    Content="跳过"
                    Style="{StaticResource DialogButtonStyle}"
                    Click="SkipBtn_Click"/>

            <Button Name="CancelBtn"
                    Content="取消"
                    Style="{StaticResource DialogButtonStyle}"
                    Click="CancelBtn_Click"/>
        </StackPanel>
    </Grid>
</Window>
