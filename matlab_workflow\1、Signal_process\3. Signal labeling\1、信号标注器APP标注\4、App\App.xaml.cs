using System;
using System.IO;
using System.Windows;
using BowelSoundLabeler.Services;

namespace BowelSoundLabeler
{
    /// <summary>
    /// 肠鸣音标注器应用程序主类
    /// </summary>
    public partial class App : Application
    {
        /// <summary>
        /// 应用程序启动时的处理
        /// </summary>
        /// <param name="e">启动事件参数</param>
        protected override void OnStartup(StartupEventArgs e)
        {
            base.OnStartup(e);
            
            try
            {
                // 初始化应用程序
                InitializeApplication();
                
                // 设置全局异常处理
                SetupExceptionHandling();
                
                // 记录应用程序启动
                LogService.Info("肠鸣音标注器应用程序启动");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"应用程序启动失败：{ex.Message}", "启动错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown(1);
            }
        }

        /// <summary>
        /// 应用程序退出时的处理
        /// </summary>
        /// <param name="e">退出事件参数</param>
        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // 保存应用程序设置
                ConfigService.Instance.SaveSettings();
                
                // 记录应用程序退出
                LogService.Info("肠鸣音标注器应用程序退出");
            }
            catch (Exception ex)
            {
                // 退出时的异常不应该阻止程序关闭
                LogService.Error($"应用程序退出时发生错误：{ex.Message}");
            }
            
            base.OnExit(e);
        }

        /// <summary>
        /// 初始化应用程序
        /// </summary>
        private void InitializeApplication()
        {
            // 确保应用程序数据目录存在
            var appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "BowelSoundLabeler");
            
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }
            
            // 初始化配置服务
            ConfigService.Instance.Initialize();
            
            // 初始化日志服务
            LogService.Initialize(Path.Combine(appDataPath, "Logs"));
        }

        /// <summary>
        /// 设置全局异常处理
        /// </summary>
        private void SetupExceptionHandling()
        {
            // 处理UI线程未捕获的异常
            DispatcherUnhandledException += (sender, e) =>
            {
                LogService.Error($"UI线程未捕获异常：{e.Exception}");
                
                var result = MessageBox.Show(
                    $"应用程序遇到未处理的错误：\n\n{e.Exception.Message}\n\n是否继续运行？",
                    "错误",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Error);
                
                if (result == MessageBoxResult.Yes)
                {
                    e.Handled = true;
                }
                else
                {
                    Shutdown(1);
                }
            };
            
            // 处理非UI线程未捕获的异常
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                var exception = e.ExceptionObject as Exception;
                LogService.Error($"应用程序域未捕获异常：{exception}");
                
                MessageBox.Show(
                    $"应用程序遇到严重错误：\n\n{exception?.Message ?? "未知错误"}",
                    "严重错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            };
        }
    }
}
