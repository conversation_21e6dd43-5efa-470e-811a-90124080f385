using System;
using System.IO;
using Microsoft.UI.Xaml;
using BowelSoundLabeler.Services;
using BowelSoundLabeler.Views;

namespace BowelSoundLabeler
{
    /// <summary>
    /// 肠鸣音标注器应用程序主类
    /// </summary>
    public partial class App : Application
    {
        private Window m_window;

        /// <summary>
        /// 应用程序启动时的处理
        /// </summary>
        /// <param name="args">启动事件参数</param>
        protected override void OnLaunched(LaunchActivatedEventArgs args)
        {
            try
            {
                // 初始化应用程序
                InitializeApplication();

                // 设置全局异常处理
                SetupExceptionHandling();

                // 创建主窗口
                m_window = new MainWindow();
                m_window.Activate();

                // 记录应用程序启动
                LogService.Info("肠鸣音标注器应用程序启动");
            }
            catch (Exception ex)
            {
                // WinUI 3 中使用 ContentDialog 替代 MessageBox
                LogService.Error($"应用程序启动失败：{ex.Message}");
                // 这里可以显示错误对话框，但需要在窗口创建后
            }
        }

        /// <summary>
        /// 应用程序退出时的处理
        /// </summary>
        public void OnApplicationExit()
        {
            try
            {
                // 保存应用程序设置
                ConfigService.Instance.SaveSettings();

                // 记录应用程序退出
                LogService.Info("肠鸣音标注器应用程序退出");
            }
            catch (Exception ex)
            {
                // 退出时的异常不应该阻止程序关闭
                LogService.Error($"应用程序退出时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 初始化应用程序
        /// </summary>
        private void InitializeApplication()
        {
            // 确保应用程序数据目录存在
            var appDataPath = Path.Combine(
                Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
                "BowelSoundLabeler");
            
            if (!Directory.Exists(appDataPath))
            {
                Directory.CreateDirectory(appDataPath);
            }
            
            // 初始化配置服务
            ConfigService.Instance.Initialize();
            
            // 初始化日志服务
            LogService.Initialize(Path.Combine(appDataPath, "Logs"));
        }

        /// <summary>
        /// 设置全局异常处理
        /// </summary>
        private void SetupExceptionHandling()
        {
            // 处理UI线程未捕获的异常
            UnhandledException += (sender, e) =>
            {
                LogService.Error($"UI线程未捕获异常：{e.Exception}");

                // WinUI 3 中的异常处理
                e.Handled = true; // 标记为已处理，防止应用程序崩溃

                // 可以在这里显示错误对话框或记录日志
                // 注意：WinUI 3 中不能直接使用 MessageBox
            };

            // 处理非UI线程未捕获的异常
            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                var exception = e.ExceptionObject as Exception;
                LogService.Error($"应用程序域未捕获异常：{exception}");

                // 记录严重错误
                LogService.Fatal($"应用程序遇到严重错误：{exception?.Message ?? "未知错误"}");
            };
        }
    }
}
