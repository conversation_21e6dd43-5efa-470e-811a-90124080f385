﻿#pragma checksum "..\..\..\..\..\Views\LabelingDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "F99471FDF69A642B13CD41383CDEFE8DD1CFF625"
//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace BowelSoundLabeler.Views {
    
    
    /// <summary>
    /// LabelingDialog
    /// </summary>
    public partial class LabelingDialog : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 55 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock FileNameLabel;
        
        #line default
        #line hidden
        
        
        #line 75 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button YesBtn;
        
        #line default
        #line hidden
        
        
        #line 82 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button NoBtn;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.StackPanel CountInputPanel;
        
        #line default
        #line hidden
        
        
        #line 103 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox CountTextBox;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock CountErrorLabel;
        
        #line default
        #line hidden
        
        
        #line 129 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ConfirmBtn;
        
        #line default
        #line hidden
        
        
        #line 137 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button SkipBtn;
        
        #line default
        #line hidden
        
        
        #line 144 "..\..\..\..\..\Views\LabelingDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button CancelBtn;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/BowelSoundLabeler;V1.0.0.0;component/views/labelingdialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\LabelingDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.FileNameLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.YesBtn = ((System.Windows.Controls.Button)(target));
            
            #line 80 "..\..\..\..\..\Views\LabelingDialog.xaml"
            this.YesBtn.Click += new System.Windows.RoutedEventHandler(this.YesBtn_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            this.NoBtn = ((System.Windows.Controls.Button)(target));
            
            #line 87 "..\..\..\..\..\Views\LabelingDialog.xaml"
            this.NoBtn.Click += new System.Windows.RoutedEventHandler(this.NoBtn_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            this.CountInputPanel = ((System.Windows.Controls.StackPanel)(target));
            return;
            case 5:
            this.CountTextBox = ((System.Windows.Controls.TextBox)(target));
            
            #line 111 "..\..\..\..\..\Views\LabelingDialog.xaml"
            this.CountTextBox.TextChanged += new System.Windows.Controls.TextChangedEventHandler(this.CountTextBox_TextChanged);
            
            #line default
            #line hidden
            return;
            case 6:
            this.CountErrorLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 7:
            this.ConfirmBtn = ((System.Windows.Controls.Button)(target));
            
            #line 135 "..\..\..\..\..\Views\LabelingDialog.xaml"
            this.ConfirmBtn.Click += new System.Windows.RoutedEventHandler(this.ConfirmBtn_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.SkipBtn = ((System.Windows.Controls.Button)(target));
            
            #line 142 "..\..\..\..\..\Views\LabelingDialog.xaml"
            this.SkipBtn.Click += new System.Windows.RoutedEventHandler(this.SkipBtn_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.CancelBtn = ((System.Windows.Controls.Button)(target));
            
            #line 148 "..\..\..\..\..\Views\LabelingDialog.xaml"
            this.CancelBtn.Click += new System.Windows.RoutedEventHandler(this.CancelBtn_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

