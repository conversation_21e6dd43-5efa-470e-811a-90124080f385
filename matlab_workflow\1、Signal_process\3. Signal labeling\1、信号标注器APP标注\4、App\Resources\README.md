# 资源文件说明

## 文件夹内容

这个文件夹包含应用程序所需的资源文件：

### 图标文件
- `app_icon.ico` - 应用程序主图标（需要手动添加）
  - 建议尺寸：16x16, 32x32, 48x48, 256x256 像素
  - 格式：ICO 格式
  - 用途：窗口标题栏图标、任务栏图标、桌面快捷方式图标

### 图片文件
- 可以添加其他需要的图片资源
- 支持格式：PNG, JPG, BMP, GIF

### 其他资源
- 可以添加音频文件、配置文件等其他资源

## 使用说明

1. **添加图标文件**：
   - 将准备好的 `app_icon.ico` 文件复制到此文件夹
   - 确保文件名为 `app_icon.ico`
   - 如果没有图标文件，应用程序将使用默认的 Windows 图标

2. **添加其他资源**：
   - 将资源文件复制到此文件夹
   - 在代码中使用相对路径引用：`Resources/filename.ext`

3. **构建配置**：
   - 项目文件已配置为自动包含此文件夹中的所有文件
   - 资源文件将被嵌入到最终的可执行文件中

## 注意事项

- 图标文件大小建议控制在 100KB 以内
- 图片文件建议使用 PNG 格式以获得最佳质量
- 避免使用过大的资源文件，以免影响应用程序启动速度
