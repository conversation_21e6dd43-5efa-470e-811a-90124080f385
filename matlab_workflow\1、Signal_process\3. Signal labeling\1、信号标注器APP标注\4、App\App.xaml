<Application x:Class="BowelSoundLabeler.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             StartupUri="Views/MainWindow.xaml">
    <Application.Resources>
        <!-- 全局样式定义 -->
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Styles/ButtonStyles.xaml"/>
                <ResourceDictionary Source="Styles/WindowStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 全局颜色定义 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2E7D32"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#E8F5E8"/>
            <SolidColorBrush x:Key="AccentBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="DangerBrush" Color="#F44336"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="LightGrayBrush" Color="#F5F5F5"/>
            <SolidColorBrush x:Key="DarkGrayBrush" Color="#757575"/>
            
            <!-- 全局字体定义 -->
            <FontFamily x:Key="DefaultFontFamily">Microsoft YaHei UI, Segoe UI, Arial</FontFamily>
            
            <!-- 标题样式 -->
            <Style x:Key="TitleTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource DefaultFontFamily}"/>
                <Setter Property="FontSize" Value="18"/>
                <Setter Property="FontWeight" Value="Bold"/>
                <Setter Property="Foreground" Value="{StaticResource PrimaryBrush}"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="Margin" Value="0,10"/>
            </Style>
            
            <!-- 普通文本样式 -->
            <Style x:Key="NormalTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource DefaultFontFamily}"/>
                <Setter Property="FontSize" Value="12"/>
                <Setter Property="Foreground" Value="Black"/>
                <Setter Property="TextWrapping" Value="Wrap"/>
            </Style>
            
            <!-- 状态文本样式 -->
            <Style x:Key="StatusTextStyle" TargetType="TextBlock">
                <Setter Property="FontFamily" Value="{StaticResource DefaultFontFamily}"/>
                <Setter Property="FontSize" Value="11"/>
                <Setter Property="Foreground" Value="{StaticResource DarkGrayBrush}"/>
                <Setter Property="HorizontalAlignment" Value="Center"/>
                <Setter Property="Margin" Value="5"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>
